<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Test</title>
    <link href="css/css2.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-text {
            font-family: 'Cabin', sans-serif;
            font-size: 24px;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .fallback-text {
            font-family: Arial, sans-serif;
            font-size: 24px;
            margin: 20px 0;
            padding: 20px;
            background: #ffeeee;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-info {
            font-family: monospace;
            font-size: 14px;
            background: #333;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Font Loading Test</h1>
    
    <div class="test-text">
        This text should use the Cabin font from Google Fonts.
        If you can see this text clearly, the font is loading correctly.
    </div>
    
    <div class="fallback-text">
        This text uses Arial as a fallback font for comparison.
    </div>
    
    <div class="debug-info">
        Debug Information:<br>
        - CSS file: css/css2.css<br>
        - Font files should be in: assets/ directory<br>
        - Check browser developer tools for font loading errors
    </div>
    
    <script>
        // Check if fonts are loaded
        document.fonts.ready.then(function() {
            console.log('All fonts loaded');
            const loadedFonts = [];
            document.fonts.forEach(font => {
                loadedFonts.push(font.family);
            });
            console.log('Loaded fonts:', loadedFonts);
        });
        
        // Check for font loading errors
        document.fonts.addEventListener('loadingerror', function(event) {
            console.error('Font loading error:', event);
        });
    </script>
</body>
</html>
