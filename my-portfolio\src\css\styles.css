/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    overflow: hidden;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
    background-color: #f22d40;
    color: #000;
    cursor: none;
}

/* 自定义光标 */
.custom-cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: #000;
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
}

/* Logo变形容器 */
#morphing-logo {
    position: fixed;
    left: 2.5vw;
    top: 1vh;
    height: 6vh;
    z-index: 1000;
    transform: translateX(0%);
}

#svg-logo {
    width: 100%;
    height: 100%;
}

/* 移动端警告 */
.mobile-warning {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 10000;
    justify-content: center;
    align-items: center;
    color: white;
    text-align: center;
    padding: 2rem;
}

.mobile-warning-text {
    font-size: 1.2rem;
    font-weight: 500;
    max-width: 300px;
}

@media (max-width: 768px) {
    .mobile-warning {
        display: flex;
    }

    #main-content {
        display: none !important;
    }
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-overlay.fade-out {
    opacity: 0;
    pointer-events: none;
}

.loading-container {
    text-align: center;
    color: white;
}

#loading-svg-logo {
    width: 200px;
    height: 140px;
    margin-bottom: 2rem;
}

.loading-percentage {
    font-size: 1.5rem;
    font-weight: 600;
    color: #f22d40;
}

/* 主内容 */
#main-content {
    min-height: 100vh;
}

/* 顶部导航 */
.topbar-initial-fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
}

.position-button-group-fixed {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.text-button {
    color: #000;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.text-button:hover {
    color: #666;
}

.slash1, .slash2 {
    width: 1px;
    height: 20px;
    background: #000;
}

.thin-horizontal-bar-fixed {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: #000;
}

#github {
    opacity: 1;
}

#github a {
    color: #000;
    transition: color 0.3s ease;
}

#github a:hover {
    color: #666;
}

/* 滚动容器 */
.scroll-container {
    position: relative;
    width: 100%;
    height: 100vh;
}

/* 页面section */
.page-section {
    position: absolute;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateY(100vh);
    transition: all 0.8s ease;
}

.page-section.active {
    opacity: 1;
    transform: translateY(0);
}

/* 第一页样式 */
#page1 {
    opacity: 1;
    transform: translateY(0);
}

.miniLogo {
    position: absolute;
    top: 2rem;
    left: 2rem;
    z-index: 100;
}

.topbar-initial {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    padding: 2rem;
    z-index: 100;
}

.ohmyking-space {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 0.5rem;
}

.ohmyking {
    font-size: 1rem;
    color: #666;
    margin-bottom: 2rem;
}

.position-button-group {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.thin-horizontal-bar {
    width: 100%;
    height: 1px;
    background: #000;
}

/* 背景文字动画区域 */
.information {
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.thin-vertical-bar {
    position: absolute;
    right: 2.5vw;
    top: 0;
    width: 1px;
    height: 100%;
    background: #000;
}

.background-text-ground {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    height: auto;
}

.background-text-ground > div {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    font-size: 8vw;
    font-weight: 900;
    line-height: 1;
    white-space: nowrap;
}

.learner1, .developer1, .designer1, .creator1 {
    color: #000;
    margin-right: 2rem;
    z-index: 2;
    position: relative;
}

.background-text {
    color: rgba(0, 0, 0, 0.1);
    font-weight: 900;
    animation: scrollText 20s linear infinite;
}

@keyframes scrollText {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* 其他页面样式 */
.page-content {
    text-align: center;
    max-width: 800px;
    padding: 2rem;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 2rem;
}

.about-content p {
    font-size: 1.2rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: #333;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.project-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.project-item h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #000;
}

.project-item p {
    color: #333;
    line-height: 1.6;
}

.contact-info p {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #333;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 页面切换动画 */
.page-transition-enter {
    opacity: 0;
    transform: translateY(100vh);
}

.page-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.8s ease;
}

.page-transition-exit {
    opacity: 1;
    transform: translateY(0);
}

.page-transition-exit-active {
    opacity: 0;
    transform: translateY(-100vh);
    transition: all 0.8s ease;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .background-text-ground > div {
        font-size: 10vw;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .ohmyking-space {
        font-size: 1.2rem;
    }
}

@media (max-width: 768px) {
    .topbar-initial-fixed {
        padding: 0 1rem;
    }

    .position-button-group-fixed {
        gap: 0.5rem;
    }

    .text-button {
        font-size: 12px;
    }

    .background-text-ground > div {
        font-size: 12vw;
        margin-bottom: 1rem;
    }

    .page-title {
        font-size: 2rem;
    }

    .page-content {
        padding: 1rem;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .miniLogo {
        top: 1rem;
        left: 1rem;
    }

    .topbar-initial {
        padding: 1rem;
    }

    .ohmyking-space {
        font-size: 1rem;
    }
}

/* 特殊效果 */
.text-button:hover {
    transform: scale(1.05);
}

#github a:hover svg {
    transform: scale(1.1);
}

/* 页面特定样式 */
#page2 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#page3 {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

#page4 {
    background: linear-gradient(135deg, #8360c3 0%, #2ebf91 100%);
}
