import * as THREE from 'three';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ScrollToPlugin } from 'gsap/ScrollToPlugin';

// 注册GSAP插件
gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);

// 导入模块
import { SceneManager } from './modules/SceneManager.js';
import { AnimationController } from './modules/AnimationController.js';
import { LoadingManager } from './modules/LoadingManager.js';

class App {
    constructor() {
        this.currentPage = 0;
        this.totalPages = 4;
        this.isScrolling = false;
        this.init();
    }

    async init() {
        // 显示加载动画
        this.loadingManager = new LoadingManager();
        this.loadingManager.show();

        // 初始化核心模块
        this.sceneManager = new SceneManager();
        this.animationController = new AnimationController();

        // 设置事件监听
        this.setupEventListeners();

        // 初始化页面动画
        this.initPageAnimations();

        // 初始化自定义光标
        this.initCustomCursor();

        // 初始化3D场景
        await this.init3DScenes();

        // 隐藏加载动画
        setTimeout(() => {
            this.loadingManager.hide();
        }, 1000);

        // 启动渲染循环
        this.animate();
    }

    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.sceneManager.handleResize();
        });

        // 滚轮事件
        window.addEventListener('wheel', (e) => {
            this.handleScroll(e);
        }, { passive: false });

        // 键盘事件
        window.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowDown' || e.key === ' ') {
                e.preventDefault();
                this.nextPage();
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.prevPage();
            }
        });

        // 导航按钮点击
        document.querySelectorAll('.text-button').forEach(button => {
            button.addEventListener('click', (e) => {
                const buttonText = button.textContent;
                if (buttonText.includes('WHOAMI')) {
                    this.goToPage(1);
                } else if (buttonText.includes('WORK')) {
                    this.goToPage(2);
                } else if (buttonText.includes('CONTACT')) {
                    this.goToPage(3);
                }
            });
        });

        // 触摸事件（移动端）
        let touchStartY = 0;
        window.addEventListener('touchstart', (e) => {
            touchStartY = e.touches[0].clientY;
        });

        window.addEventListener('touchend', (e) => {
            const touchEndY = e.changedTouches[0].clientY;
            const diff = touchStartY - touchEndY;

            if (Math.abs(diff) > 50) {
                if (diff > 0) {
                    this.nextPage();
                } else {
                    this.prevPage();
                }
            }
        });
    }

    handleScroll(e) {
        if (this.isScrolling) return;

        e.preventDefault();

        if (e.deltaY > 0) {
            this.nextPage();
        } else {
            this.prevPage();
        }
    }

    nextPage() {
        if (this.isScrolling || this.currentPage >= this.totalPages - 1) return;
        this.goToPage(this.currentPage + 1);
    }

    prevPage() {
        if (this.isScrolling || this.currentPage <= 0) return;
        this.goToPage(this.currentPage - 1);
    }

    goToPage(pageIndex) {
        if (this.isScrolling || pageIndex === this.currentPage) return;

        this.isScrolling = true;
        const currentPageElement = document.getElementById(`page${this.currentPage + 1}`);
        const targetPageElement = document.getElementById(`page${pageIndex + 1}`);

        if (currentPageElement && targetPageElement) {
            // 隐藏当前页面
            gsap.to(currentPageElement, {
                opacity: 0,
                y: pageIndex > this.currentPage ? -window.innerHeight : window.innerHeight,
                duration: 0.8,
                ease: "power2.inOut"
            });

            // 显示目标页面
            gsap.fromTo(targetPageElement,
                {
                    opacity: 0,
                    y: pageIndex > this.currentPage ? window.innerHeight : -window.innerHeight
                },
                {
                    opacity: 1,
                    y: 0,
                    duration: 0.8,
                    ease: "power2.inOut",
                    onComplete: () => {
                        this.isScrolling = false;
                    }
                }
            );

            this.currentPage = pageIndex;
            this.updatePageIndicators();
        }
    }

    updatePageIndicators() {
        // 更新页面指示器（如果有的话）
        const indicators = document.querySelectorAll('.page-indicator');
        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === this.currentPage);
        });
    }

    initCustomCursor() {
        const cursor = document.querySelector('.custom-cursor');
        if (!cursor) return;

        document.addEventListener('mousemove', (e) => {
            gsap.to(cursor, {
                x: e.clientX - 10,
                y: e.clientY - 10,
                duration: 0.1,
                ease: "power2.out"
            });
        });

        // 鼠标悬停效果
        document.querySelectorAll('a, button, .text-button').forEach(element => {
            element.addEventListener('mouseenter', () => {
                gsap.to(cursor, {
                    scale: 1.5,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });

            element.addEventListener('mouseleave', () => {
                gsap.to(cursor, {
                    scale: 1,
                    duration: 0.3,
                    ease: "power2.out"
                });
            });
        });
    }

    initPageAnimations() {
        // 背景文字滚动动画
        const backgroundTexts = document.querySelectorAll('.background-text');
        backgroundTexts.forEach(text => {
            gsap.to(text, {
                x: '-100%',
                duration: 20,
                ease: 'none',
                repeat: -1
            });
        });

        // Logo变形动画
        const logo = document.getElementById('morphing-logo');
        if (logo) {
            gsap.to(logo, {
                rotation: 360,
                duration: 60,
                ease: 'none',
                repeat: -1
            });
        }

        // 页面入场动画
        const pages = document.querySelectorAll('.page-section');
        pages.forEach((page, index) => {
            if (index === 0) return; // 第一页默认显示

            gsap.set(page, {
                opacity: 0,
                y: window.innerHeight
            });
        });

        // 文字打字机效果
        this.initTypewriterEffect();
    }

    initTypewriterEffect() {
        const titles = document.querySelectorAll('.learner1, .developer1, .designer1, .creator1');
        titles.forEach((title, index) => {
            const text = title.textContent;
            title.textContent = '';

            gsap.to(title, {
                duration: 2,
                delay: index * 0.5,
                ease: "none",
                onUpdate: function() {
                    const progress = this.progress();
                    const currentLength = Math.floor(progress * text.length);
                    title.textContent = text.substring(0, currentLength);
                }
            });
        });
    }

    async init3DScenes() {
        // 可以在这里添加3D场景，目前先保持简单
        console.log('3D scenes initialized');
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        // 更新所有3D场景
        this.sceneManager.update();

        // 更新动画控制器
        this.animationController.update();
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new App();
});

// 导出App类供其他模块使用
export default App;
