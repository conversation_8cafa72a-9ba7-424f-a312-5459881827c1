{"version": 3, "sources": ["../../gsap/Observer.js", "../../gsap/ScrollTrigger.js"], "sourcesContent": ["function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\n/*!\n * Observer 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nvar gsap,\n    _coreInitted,\n    _clamp,\n    _win,\n    _doc,\n    _docEl,\n    _body,\n    _isTouch,\n    _pointerType,\n    ScrollTrigger,\n    _root,\n    _normalizer,\n    _eventTypes,\n    _context,\n    _getGSAP = function _getGSAP() {\n  return gsap || typeof window !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap;\n},\n    _startup = 1,\n    _observers = [],\n    _scrollers = [],\n    _proxies = [],\n    _getTime = Date.now,\n    _bridge = function _bridge(name, value) {\n  return value;\n},\n    _integrate = function _integrate() {\n  var core = ScrollTrigger.core,\n      data = core.bridge || {},\n      scrollers = core._scrollers,\n      proxies = core._proxies;\n  scrollers.push.apply(scrollers, _scrollers);\n  proxies.push.apply(proxies, _proxies);\n  _scrollers = scrollers;\n  _proxies = proxies;\n\n  _bridge = function _bridge(name, value) {\n    return data[name](value);\n  };\n},\n    _getProxyProp = function _getProxyProp(element, property) {\n  return ~_proxies.indexOf(element) && _proxies[_proxies.indexOf(element) + 1][property];\n},\n    _isViewport = function _isViewport(el) {\n  return !!~_root.indexOf(el);\n},\n    _addListener = function _addListener(element, type, func, passive, capture) {\n  return element.addEventListener(type, func, {\n    passive: passive !== false,\n    capture: !!capture\n  });\n},\n    _removeListener = function _removeListener(element, type, func, capture) {\n  return element.removeEventListener(type, func, !!capture);\n},\n    _scrollLeft = \"scrollLeft\",\n    _scrollTop = \"scrollTop\",\n    _onScroll = function _onScroll() {\n  return _normalizer && _normalizer.isPressed || _scrollers.cache++;\n},\n    _scrollCacheFunc = function _scrollCacheFunc(f, doNotCache) {\n  var cachingFunc = function cachingFunc(value) {\n    // since reading the scrollTop/scrollLeft/pageOffsetY/pageOffsetX can trigger a layout, this function allows us to cache the value so it only gets read fresh after a \"scroll\" event fires (or while we're refreshing because that can lengthen the page and alter the scroll position). when \"soft\" is true, that means don't actually set the scroll, but cache the new value instead (useful in ScrollSmoother)\n    if (value || value === 0) {\n      _startup && (_win.history.scrollRestoration = \"manual\"); // otherwise the new position will get overwritten by the browser onload.\n\n      var isNormalizing = _normalizer && _normalizer.isPressed;\n      value = cachingFunc.v = Math.round(value) || (_normalizer && _normalizer.iOS ? 1 : 0); //TODO: iOS Bug: if you allow it to go to 0, Safari can start to report super strange (wildly inaccurate) touch positions!\n\n      f(value);\n      cachingFunc.cacheID = _scrollers.cache;\n      isNormalizing && _bridge(\"ss\", value); // set scroll (notify ScrollTrigger so it can dispatch a \"scrollStart\" event if necessary\n    } else if (doNotCache || _scrollers.cache !== cachingFunc.cacheID || _bridge(\"ref\")) {\n      cachingFunc.cacheID = _scrollers.cache;\n      cachingFunc.v = f();\n    }\n\n    return cachingFunc.v + cachingFunc.offset;\n  };\n\n  cachingFunc.offset = 0;\n  return f && cachingFunc;\n},\n    _horizontal = {\n  s: _scrollLeft,\n  p: \"left\",\n  p2: \"Left\",\n  os: \"right\",\n  os2: \"Right\",\n  d: \"width\",\n  d2: \"Width\",\n  a: \"x\",\n  sc: _scrollCacheFunc(function (value) {\n    return arguments.length ? _win.scrollTo(value, _vertical.sc()) : _win.pageXOffset || _doc[_scrollLeft] || _docEl[_scrollLeft] || _body[_scrollLeft] || 0;\n  })\n},\n    _vertical = {\n  s: _scrollTop,\n  p: \"top\",\n  p2: \"Top\",\n  os: \"bottom\",\n  os2: \"Bottom\",\n  d: \"height\",\n  d2: \"Height\",\n  a: \"y\",\n  op: _horizontal,\n  sc: _scrollCacheFunc(function (value) {\n    return arguments.length ? _win.scrollTo(_horizontal.sc(), value) : _win.pageYOffset || _doc[_scrollTop] || _docEl[_scrollTop] || _body[_scrollTop] || 0;\n  })\n},\n    _getTarget = function _getTarget(t, self) {\n  return (self && self._ctx && self._ctx.selector || gsap.utils.toArray)(t)[0] || (typeof t === \"string\" && gsap.config().nullTargetWarn !== false ? console.warn(\"Element not found:\", t) : null);\n},\n    _isWithin = function _isWithin(element, list) {\n  // check if the element is in the list or is a descendant of an element in the list.\n  var i = list.length;\n\n  while (i--) {\n    if (list[i] === element || list[i].contains(element)) {\n      return true;\n    }\n  }\n\n  return false;\n},\n    _getScrollFunc = function _getScrollFunc(element, _ref) {\n  var s = _ref.s,\n      sc = _ref.sc;\n  // we store the scroller functions in an alternating sequenced Array like [element, verticalScrollFunc, horizontalScrollFunc, ...] so that we can minimize memory, maximize performance, and we also record the last position as a \".rec\" property in order to revert to that after refreshing to ensure things don't shift around.\n  _isViewport(element) && (element = _doc.scrollingElement || _docEl);\n\n  var i = _scrollers.indexOf(element),\n      offset = sc === _vertical.sc ? 1 : 2;\n\n  !~i && (i = _scrollers.push(element) - 1);\n  _scrollers[i + offset] || _addListener(element, \"scroll\", _onScroll); // clear the cache when a scroll occurs\n\n  var prev = _scrollers[i + offset],\n      func = prev || (_scrollers[i + offset] = _scrollCacheFunc(_getProxyProp(element, s), true) || (_isViewport(element) ? sc : _scrollCacheFunc(function (value) {\n    return arguments.length ? element[s] = value : element[s];\n  })));\n  func.target = element;\n  prev || (func.smooth = gsap.getProperty(element, \"scrollBehavior\") === \"smooth\"); // only set it the first time (don't reset every time a scrollFunc is requested because perhaps it happens during a refresh() when it's disabled in ScrollTrigger.\n\n  return func;\n},\n    _getVelocityProp = function _getVelocityProp(value, minTimeRefresh, useDelta) {\n  var v1 = value,\n      v2 = value,\n      t1 = _getTime(),\n      t2 = t1,\n      min = minTimeRefresh || 50,\n      dropToZeroTime = Math.max(500, min * 3),\n      update = function update(value, force) {\n    var t = _getTime();\n\n    if (force || t - t1 > min) {\n      v2 = v1;\n      v1 = value;\n      t2 = t1;\n      t1 = t;\n    } else if (useDelta) {\n      v1 += value;\n    } else {\n      // not totally necessary, but makes it a bit more accurate by adjusting the v1 value according to the new slope. This way we're not just ignoring the incoming data. Removing for now because it doesn't seem to make much practical difference and it's probably not worth the kb.\n      v1 = v2 + (value - v2) / (t - t2) * (t1 - t2);\n    }\n  },\n      reset = function reset() {\n    v2 = v1 = useDelta ? 0 : v1;\n    t2 = t1 = 0;\n  },\n      getVelocity = function getVelocity(latestValue) {\n    var tOld = t2,\n        vOld = v2,\n        t = _getTime();\n\n    (latestValue || latestValue === 0) && latestValue !== v1 && update(latestValue);\n    return t1 === t2 || t - t2 > dropToZeroTime ? 0 : (v1 + (useDelta ? vOld : -vOld)) / ((useDelta ? t : t1) - tOld) * 1000;\n  };\n\n  return {\n    update: update,\n    reset: reset,\n    getVelocity: getVelocity\n  };\n},\n    _getEvent = function _getEvent(e, preventDefault) {\n  preventDefault && !e._gsapAllow && e.preventDefault();\n  return e.changedTouches ? e.changedTouches[0] : e;\n},\n    _getAbsoluteMax = function _getAbsoluteMax(a) {\n  var max = Math.max.apply(Math, a),\n      min = Math.min.apply(Math, a);\n  return Math.abs(max) >= Math.abs(min) ? max : min;\n},\n    _setScrollTrigger = function _setScrollTrigger() {\n  ScrollTrigger = gsap.core.globals().ScrollTrigger;\n  ScrollTrigger && ScrollTrigger.core && _integrate();\n},\n    _initCore = function _initCore(core) {\n  gsap = core || _getGSAP();\n\n  if (!_coreInitted && gsap && typeof document !== \"undefined\" && document.body) {\n    _win = window;\n    _doc = document;\n    _docEl = _doc.documentElement;\n    _body = _doc.body;\n    _root = [_win, _doc, _docEl, _body];\n    _clamp = gsap.utils.clamp;\n\n    _context = gsap.core.context || function () {};\n\n    _pointerType = \"onpointerenter\" in _body ? \"pointer\" : \"mouse\"; // isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\n    _isTouch = Observer.isTouch = _win.matchMedia && _win.matchMedia(\"(hover: none), (pointer: coarse)\").matches ? 1 : \"ontouchstart\" in _win || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0 ? 2 : 0;\n    _eventTypes = Observer.eventTypes = (\"ontouchstart\" in _docEl ? \"touchstart,touchmove,touchcancel,touchend\" : !(\"onpointerdown\" in _docEl) ? \"mousedown,mousemove,mouseup,mouseup\" : \"pointerdown,pointermove,pointercancel,pointerup\").split(\",\");\n    setTimeout(function () {\n      return _startup = 0;\n    }, 500);\n\n    _setScrollTrigger();\n\n    _coreInitted = 1;\n  }\n\n  return _coreInitted;\n};\n\n_horizontal.op = _vertical;\n_scrollers.cache = 0;\nexport var Observer = /*#__PURE__*/function () {\n  function Observer(vars) {\n    this.init(vars);\n  }\n\n  var _proto = Observer.prototype;\n\n  _proto.init = function init(vars) {\n    _coreInitted || _initCore(gsap) || console.warn(\"Please gsap.registerPlugin(Observer)\");\n    ScrollTrigger || _setScrollTrigger();\n    var tolerance = vars.tolerance,\n        dragMinimum = vars.dragMinimum,\n        type = vars.type,\n        target = vars.target,\n        lineHeight = vars.lineHeight,\n        debounce = vars.debounce,\n        preventDefault = vars.preventDefault,\n        onStop = vars.onStop,\n        onStopDelay = vars.onStopDelay,\n        ignore = vars.ignore,\n        wheelSpeed = vars.wheelSpeed,\n        event = vars.event,\n        onDragStart = vars.onDragStart,\n        onDragEnd = vars.onDragEnd,\n        onDrag = vars.onDrag,\n        onPress = vars.onPress,\n        onRelease = vars.onRelease,\n        onRight = vars.onRight,\n        onLeft = vars.onLeft,\n        onUp = vars.onUp,\n        onDown = vars.onDown,\n        onChangeX = vars.onChangeX,\n        onChangeY = vars.onChangeY,\n        onChange = vars.onChange,\n        onToggleX = vars.onToggleX,\n        onToggleY = vars.onToggleY,\n        onHover = vars.onHover,\n        onHoverEnd = vars.onHoverEnd,\n        onMove = vars.onMove,\n        ignoreCheck = vars.ignoreCheck,\n        isNormalizer = vars.isNormalizer,\n        onGestureStart = vars.onGestureStart,\n        onGestureEnd = vars.onGestureEnd,\n        onWheel = vars.onWheel,\n        onEnable = vars.onEnable,\n        onDisable = vars.onDisable,\n        onClick = vars.onClick,\n        scrollSpeed = vars.scrollSpeed,\n        capture = vars.capture,\n        allowClicks = vars.allowClicks,\n        lockAxis = vars.lockAxis,\n        onLockAxis = vars.onLockAxis;\n    this.target = target = _getTarget(target) || _docEl;\n    this.vars = vars;\n    ignore && (ignore = gsap.utils.toArray(ignore));\n    tolerance = tolerance || 1e-9;\n    dragMinimum = dragMinimum || 0;\n    wheelSpeed = wheelSpeed || 1;\n    scrollSpeed = scrollSpeed || 1;\n    type = type || \"wheel,touch,pointer\";\n    debounce = debounce !== false;\n    lineHeight || (lineHeight = parseFloat(_win.getComputedStyle(_body).lineHeight) || 22); // note: browser may report \"normal\", so default to 22.\n\n    var id,\n        onStopDelayedCall,\n        dragged,\n        moved,\n        wheeled,\n        locked,\n        axis,\n        self = this,\n        prevDeltaX = 0,\n        prevDeltaY = 0,\n        passive = vars.passive || !preventDefault && vars.passive !== false,\n        scrollFuncX = _getScrollFunc(target, _horizontal),\n        scrollFuncY = _getScrollFunc(target, _vertical),\n        scrollX = scrollFuncX(),\n        scrollY = scrollFuncY(),\n        limitToTouch = ~type.indexOf(\"touch\") && !~type.indexOf(\"pointer\") && _eventTypes[0] === \"pointerdown\",\n        // for devices that accommodate mouse events and touch events, we need to distinguish.\n    isViewport = _isViewport(target),\n        ownerDoc = target.ownerDocument || _doc,\n        deltaX = [0, 0, 0],\n        // wheel, scroll, pointer/touch\n    deltaY = [0, 0, 0],\n        onClickTime = 0,\n        clickCapture = function clickCapture() {\n      return onClickTime = _getTime();\n    },\n        _ignoreCheck = function _ignoreCheck(e, isPointerOrTouch) {\n      return (self.event = e) && ignore && _isWithin(e.target, ignore) || isPointerOrTouch && limitToTouch && e.pointerType !== \"touch\" || ignoreCheck && ignoreCheck(e, isPointerOrTouch);\n    },\n        onStopFunc = function onStopFunc() {\n      self._vx.reset();\n\n      self._vy.reset();\n\n      onStopDelayedCall.pause();\n      onStop && onStop(self);\n    },\n        update = function update() {\n      var dx = self.deltaX = _getAbsoluteMax(deltaX),\n          dy = self.deltaY = _getAbsoluteMax(deltaY),\n          changedX = Math.abs(dx) >= tolerance,\n          changedY = Math.abs(dy) >= tolerance;\n\n      onChange && (changedX || changedY) && onChange(self, dx, dy, deltaX, deltaY); // in ScrollTrigger.normalizeScroll(), we need to know if it was touch/pointer so we need access to the deltaX/deltaY Arrays before we clear them out.\n\n      if (changedX) {\n        onRight && self.deltaX > 0 && onRight(self);\n        onLeft && self.deltaX < 0 && onLeft(self);\n        onChangeX && onChangeX(self);\n        onToggleX && self.deltaX < 0 !== prevDeltaX < 0 && onToggleX(self);\n        prevDeltaX = self.deltaX;\n        deltaX[0] = deltaX[1] = deltaX[2] = 0;\n      }\n\n      if (changedY) {\n        onDown && self.deltaY > 0 && onDown(self);\n        onUp && self.deltaY < 0 && onUp(self);\n        onChangeY && onChangeY(self);\n        onToggleY && self.deltaY < 0 !== prevDeltaY < 0 && onToggleY(self);\n        prevDeltaY = self.deltaY;\n        deltaY[0] = deltaY[1] = deltaY[2] = 0;\n      }\n\n      if (moved || dragged) {\n        onMove && onMove(self);\n\n        if (dragged) {\n          onDragStart && dragged === 1 && onDragStart(self);\n          onDrag && onDrag(self);\n          dragged = 0;\n        }\n\n        moved = false;\n      }\n\n      locked && !(locked = false) && onLockAxis && onLockAxis(self);\n\n      if (wheeled) {\n        onWheel(self);\n        wheeled = false;\n      }\n\n      id = 0;\n    },\n        onDelta = function onDelta(x, y, index) {\n      deltaX[index] += x;\n      deltaY[index] += y;\n\n      self._vx.update(x);\n\n      self._vy.update(y);\n\n      debounce ? id || (id = requestAnimationFrame(update)) : update();\n    },\n        onTouchOrPointerDelta = function onTouchOrPointerDelta(x, y) {\n      if (lockAxis && !axis) {\n        self.axis = axis = Math.abs(x) > Math.abs(y) ? \"x\" : \"y\";\n        locked = true;\n      }\n\n      if (axis !== \"y\") {\n        deltaX[2] += x;\n\n        self._vx.update(x, true); // update the velocity as frequently as possible instead of in the debounced function so that very quick touch-scrolls (flicks) feel natural. If it's the mouse/touch/pointer, force it so that we get snappy/accurate momentum scroll.\n\n      }\n\n      if (axis !== \"x\") {\n        deltaY[2] += y;\n\n        self._vy.update(y, true);\n      }\n\n      debounce ? id || (id = requestAnimationFrame(update)) : update();\n    },\n        _onDrag = function _onDrag(e) {\n      if (_ignoreCheck(e, 1)) {\n        return;\n      }\n\n      e = _getEvent(e, preventDefault);\n      var x = e.clientX,\n          y = e.clientY,\n          dx = x - self.x,\n          dy = y - self.y,\n          isDragging = self.isDragging;\n      self.x = x;\n      self.y = y;\n\n      if (isDragging || (dx || dy) && (Math.abs(self.startX - x) >= dragMinimum || Math.abs(self.startY - y) >= dragMinimum)) {\n        dragged = isDragging ? 2 : 1; // dragged: 0 = not dragging, 1 = first drag, 2 = normal drag\n\n        isDragging || (self.isDragging = true);\n        onTouchOrPointerDelta(dx, dy);\n      }\n    },\n        _onPress = self.onPress = function (e) {\n      if (_ignoreCheck(e, 1) || e && e.button) {\n        return;\n      }\n\n      self.axis = axis = null;\n      onStopDelayedCall.pause();\n      self.isPressed = true;\n      e = _getEvent(e); // note: may need to preventDefault(?) Won't side-scroll on iOS Safari if we do, though.\n\n      prevDeltaX = prevDeltaY = 0;\n      self.startX = self.x = e.clientX;\n      self.startY = self.y = e.clientY;\n\n      self._vx.reset(); // otherwise the t2 may be stale if the user touches and flicks super fast and releases in less than 2 requestAnimationFrame ticks, causing velocity to be 0.\n\n\n      self._vy.reset();\n\n      _addListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, passive, true);\n\n      self.deltaX = self.deltaY = 0;\n      onPress && onPress(self);\n    },\n        _onRelease = self.onRelease = function (e) {\n      if (_ignoreCheck(e, 1)) {\n        return;\n      }\n\n      _removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n\n      var isTrackingDrag = !isNaN(self.y - self.startY),\n          wasDragging = self.isDragging,\n          isDragNotClick = wasDragging && (Math.abs(self.x - self.startX) > 3 || Math.abs(self.y - self.startY) > 3),\n          // some touch devices need some wiggle room in terms of sensing clicks - the finger may move a few pixels.\n      eventData = _getEvent(e);\n\n      if (!isDragNotClick && isTrackingDrag) {\n        self._vx.reset();\n\n        self._vy.reset(); //if (preventDefault && allowClicks && self.isPressed) { // check isPressed because in a rare edge case, the inputObserver in ScrollTrigger may stopPropagation() on the press/drag, so the onRelease may get fired without the onPress/onDrag ever getting called, thus it could trigger a click to occur on a link after scroll-dragging it.\n\n\n        if (preventDefault && allowClicks) {\n          gsap.delayedCall(0.08, function () {\n            // some browsers (like Firefox) won't trust script-generated clicks, so if the user tries to click on a video to play it, for example, it simply won't work. Since a regular \"click\" event will most likely be generated anyway (one that has its isTrusted flag set to true), we must slightly delay our script-generated click so that the \"real\"/trusted one is prioritized. Remember, when there are duplicate events in quick succession, we suppress all but the first one. Some browsers don't even trigger the \"real\" one at all, so our synthetic one is a safety valve that ensures that no matter what, a click event does get dispatched.\n            if (_getTime() - onClickTime > 300 && !e.defaultPrevented) {\n              if (e.target.click) {\n                //some browsers (like mobile Safari) don't properly trigger the click event\n                e.target.click();\n              } else if (ownerDoc.createEvent) {\n                var syntheticEvent = ownerDoc.createEvent(\"MouseEvents\");\n                syntheticEvent.initMouseEvent(\"click\", true, true, _win, 1, eventData.screenX, eventData.screenY, eventData.clientX, eventData.clientY, false, false, false, false, 0, null);\n                e.target.dispatchEvent(syntheticEvent);\n              }\n            }\n          });\n        }\n      }\n\n      self.isDragging = self.isGesturing = self.isPressed = false;\n      onStop && wasDragging && !isNormalizer && onStopDelayedCall.restart(true);\n      dragged && update(); // in case debouncing, we don't want onDrag to fire AFTER onDragEnd().\n\n      onDragEnd && wasDragging && onDragEnd(self);\n      onRelease && onRelease(self, isDragNotClick);\n    },\n        _onGestureStart = function _onGestureStart(e) {\n      return e.touches && e.touches.length > 1 && (self.isGesturing = true) && onGestureStart(e, self.isDragging);\n    },\n        _onGestureEnd = function _onGestureEnd() {\n      return (self.isGesturing = false) || onGestureEnd(self);\n    },\n        onScroll = function onScroll(e) {\n      if (_ignoreCheck(e)) {\n        return;\n      }\n\n      var x = scrollFuncX(),\n          y = scrollFuncY();\n      onDelta((x - scrollX) * scrollSpeed, (y - scrollY) * scrollSpeed, 1);\n      scrollX = x;\n      scrollY = y;\n      onStop && onStopDelayedCall.restart(true);\n    },\n        _onWheel = function _onWheel(e) {\n      if (_ignoreCheck(e)) {\n        return;\n      }\n\n      e = _getEvent(e, preventDefault);\n      onWheel && (wheeled = true);\n      var multiplier = (e.deltaMode === 1 ? lineHeight : e.deltaMode === 2 ? _win.innerHeight : 1) * wheelSpeed;\n      onDelta(e.deltaX * multiplier, e.deltaY * multiplier, 0);\n      onStop && !isNormalizer && onStopDelayedCall.restart(true);\n    },\n        _onMove = function _onMove(e) {\n      if (_ignoreCheck(e)) {\n        return;\n      }\n\n      var x = e.clientX,\n          y = e.clientY,\n          dx = x - self.x,\n          dy = y - self.y;\n      self.x = x;\n      self.y = y;\n      moved = true;\n      onStop && onStopDelayedCall.restart(true);\n      (dx || dy) && onTouchOrPointerDelta(dx, dy);\n    },\n        _onHover = function _onHover(e) {\n      self.event = e;\n      onHover(self);\n    },\n        _onHoverEnd = function _onHoverEnd(e) {\n      self.event = e;\n      onHoverEnd(self);\n    },\n        _onClick = function _onClick(e) {\n      return _ignoreCheck(e) || _getEvent(e, preventDefault) && onClick(self);\n    };\n\n    onStopDelayedCall = self._dc = gsap.delayedCall(onStopDelay || 0.25, onStopFunc).pause();\n    self.deltaX = self.deltaY = 0;\n    self._vx = _getVelocityProp(0, 50, true);\n    self._vy = _getVelocityProp(0, 50, true);\n    self.scrollX = scrollFuncX;\n    self.scrollY = scrollFuncY;\n    self.isDragging = self.isGesturing = self.isPressed = false;\n\n    _context(this);\n\n    self.enable = function (e) {\n      if (!self.isEnabled) {\n        _addListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\n        type.indexOf(\"scroll\") >= 0 && _addListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, passive, capture);\n        type.indexOf(\"wheel\") >= 0 && _addListener(target, \"wheel\", _onWheel, passive, capture);\n\n        if (type.indexOf(\"touch\") >= 0 && _isTouch || type.indexOf(\"pointer\") >= 0) {\n          _addListener(target, _eventTypes[0], _onPress, passive, capture);\n\n          _addListener(ownerDoc, _eventTypes[2], _onRelease);\n\n          _addListener(ownerDoc, _eventTypes[3], _onRelease);\n\n          allowClicks && _addListener(target, \"click\", clickCapture, true, true);\n          onClick && _addListener(target, \"click\", _onClick);\n          onGestureStart && _addListener(ownerDoc, \"gesturestart\", _onGestureStart);\n          onGestureEnd && _addListener(ownerDoc, \"gestureend\", _onGestureEnd);\n          onHover && _addListener(target, _pointerType + \"enter\", _onHover);\n          onHoverEnd && _addListener(target, _pointerType + \"leave\", _onHoverEnd);\n          onMove && _addListener(target, _pointerType + \"move\", _onMove);\n        }\n\n        self.isEnabled = true;\n        self.isDragging = self.isGesturing = self.isPressed = moved = dragged = false;\n\n        self._vx.reset();\n\n        self._vy.reset();\n\n        scrollX = scrollFuncX();\n        scrollY = scrollFuncY();\n        e && e.type && _onPress(e);\n        onEnable && onEnable(self);\n      }\n\n      return self;\n    };\n\n    self.disable = function () {\n      if (self.isEnabled) {\n        // only remove the _onScroll listener if there aren't any others that rely on the functionality.\n        _observers.filter(function (o) {\n          return o !== self && _isViewport(o.target);\n        }).length || _removeListener(isViewport ? ownerDoc : target, \"scroll\", _onScroll);\n\n        if (self.isPressed) {\n          self._vx.reset();\n\n          self._vy.reset();\n\n          _removeListener(isNormalizer ? target : ownerDoc, _eventTypes[1], _onDrag, true);\n        }\n\n        _removeListener(isViewport ? ownerDoc : target, \"scroll\", onScroll, capture);\n\n        _removeListener(target, \"wheel\", _onWheel, capture);\n\n        _removeListener(target, _eventTypes[0], _onPress, capture);\n\n        _removeListener(ownerDoc, _eventTypes[2], _onRelease);\n\n        _removeListener(ownerDoc, _eventTypes[3], _onRelease);\n\n        _removeListener(target, \"click\", clickCapture, true);\n\n        _removeListener(target, \"click\", _onClick);\n\n        _removeListener(ownerDoc, \"gesturestart\", _onGestureStart);\n\n        _removeListener(ownerDoc, \"gestureend\", _onGestureEnd);\n\n        _removeListener(target, _pointerType + \"enter\", _onHover);\n\n        _removeListener(target, _pointerType + \"leave\", _onHoverEnd);\n\n        _removeListener(target, _pointerType + \"move\", _onMove);\n\n        self.isEnabled = self.isPressed = self.isDragging = false;\n        onDisable && onDisable(self);\n      }\n    };\n\n    self.kill = self.revert = function () {\n      self.disable();\n\n      var i = _observers.indexOf(self);\n\n      i >= 0 && _observers.splice(i, 1);\n      _normalizer === self && (_normalizer = 0);\n    };\n\n    _observers.push(self);\n\n    isNormalizer && _isViewport(target) && (_normalizer = self);\n    self.enable(event);\n  };\n\n  _createClass(Observer, [{\n    key: \"velocityX\",\n    get: function get() {\n      return this._vx.getVelocity();\n    }\n  }, {\n    key: \"velocityY\",\n    get: function get() {\n      return this._vy.getVelocity();\n    }\n  }]);\n\n  return Observer;\n}();\nObserver.version = \"3.13.0\";\n\nObserver.create = function (vars) {\n  return new Observer(vars);\n};\n\nObserver.register = _initCore;\n\nObserver.getAll = function () {\n  return _observers.slice();\n};\n\nObserver.getById = function (id) {\n  return _observers.filter(function (o) {\n    return o.vars.id === id;\n  })[0];\n};\n\n_getGSAP() && gsap.registerPlugin(Observer);\nexport { Observer as default, _isViewport, _scrollers, _getScrollFunc, _getProxyProp, _proxies, _getVelocityProp, _vertical, _horizontal, _getTarget };", "/*!\n * ScrollTrigger 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nimport { Observer, _getTarget, _vertical, _horizontal, _scrollers, _proxies, _getScrollFunc, _getProxyProp, _getVelocityProp } from \"./Observer.js\";\n\nvar gsap,\n    _coreInitted,\n    _win,\n    _doc,\n    _docEl,\n    _body,\n    _root,\n    _resizeDelay,\n    _toArray,\n    _clamp,\n    _time2,\n    _syncInterval,\n    _refreshing,\n    _pointerIsDown,\n    _transformProp,\n    _i,\n    _prevWidth,\n    _prevHeight,\n    _autoRefresh,\n    _sort,\n    _suppressOverwrites,\n    _ignoreResize,\n    _normalizer,\n    _ignoreMobileResize,\n    _baseScreenHeight,\n    _baseScreenWidth,\n    _fixIOSBug,\n    _context,\n    _scrollRestoration,\n    _div100vh,\n    _100vh,\n    _isReverted,\n    _clampingMax,\n    _limitCallbacks,\n    // if true, we'll only trigger callbacks if the active state toggles, so if you scroll immediately past both the start and end positions of a ScrollTrigger (thus inactive to inactive), neither its onEnter nor onLeave will be called. This is useful during startup.\n_startup = 1,\n    _getTime = Date.now,\n    _time1 = _getTime(),\n    _lastScrollTime = 0,\n    _enabled = 0,\n    _parseClamp = function _parseClamp(value, type, self) {\n  var clamp = _isString(value) && (value.substr(0, 6) === \"clamp(\" || value.indexOf(\"max\") > -1);\n  self[\"_\" + type + \"Clamp\"] = clamp;\n  return clamp ? value.substr(6, value.length - 7) : value;\n},\n    _keepClamp = function _keepClamp(value, clamp) {\n  return clamp && (!_isString(value) || value.substr(0, 6) !== \"clamp(\") ? \"clamp(\" + value + \")\" : value;\n},\n    _rafBugFix = function _rafBugFix() {\n  return _enabled && requestAnimationFrame(_rafBugFix);\n},\n    // in some browsers (like Firefox), screen repaints weren't consistent unless we had SOMETHING queued up in requestAnimationFrame()! So this just creates a super simple loop to keep it alive and smooth out repaints.\n_pointerDownHandler = function _pointerDownHandler() {\n  return _pointerIsDown = 1;\n},\n    _pointerUpHandler = function _pointerUpHandler() {\n  return _pointerIsDown = 0;\n},\n    _passThrough = function _passThrough(v) {\n  return v;\n},\n    _round = function _round(value) {\n  return Math.round(value * 100000) / 100000 || 0;\n},\n    _windowExists = function _windowExists() {\n  return typeof window !== \"undefined\";\n},\n    _getGSAP = function _getGSAP() {\n  return gsap || _windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap;\n},\n    _isViewport = function _isViewport(e) {\n  return !!~_root.indexOf(e);\n},\n    _getViewportDimension = function _getViewportDimension(dimensionProperty) {\n  return (dimensionProperty === \"Height\" ? _100vh : _win[\"inner\" + dimensionProperty]) || _docEl[\"client\" + dimensionProperty] || _body[\"client\" + dimensionProperty];\n},\n    _getBoundsFunc = function _getBoundsFunc(element) {\n  return _getProxyProp(element, \"getBoundingClientRect\") || (_isViewport(element) ? function () {\n    _winOffsets.width = _win.innerWidth;\n    _winOffsets.height = _100vh;\n    return _winOffsets;\n  } : function () {\n    return _getBounds(element);\n  });\n},\n    _getSizeFunc = function _getSizeFunc(scroller, isViewport, _ref) {\n  var d = _ref.d,\n      d2 = _ref.d2,\n      a = _ref.a;\n  return (a = _getProxyProp(scroller, \"getBoundingClientRect\")) ? function () {\n    return a()[d];\n  } : function () {\n    return (isViewport ? _getViewportDimension(d2) : scroller[\"client\" + d2]) || 0;\n  };\n},\n    _getOffsetsFunc = function _getOffsetsFunc(element, isViewport) {\n  return !isViewport || ~_proxies.indexOf(element) ? _getBoundsFunc(element) : function () {\n    return _winOffsets;\n  };\n},\n    _maxScroll = function _maxScroll(element, _ref2) {\n  var s = _ref2.s,\n      d2 = _ref2.d2,\n      d = _ref2.d,\n      a = _ref2.a;\n  return Math.max(0, (s = \"scroll\" + d2) && (a = _getProxyProp(element, s)) ? a() - _getBoundsFunc(element)()[d] : _isViewport(element) ? (_docEl[s] || _body[s]) - _getViewportDimension(d2) : element[s] - element[\"offset\" + d2]);\n},\n    _iterateAutoRefresh = function _iterateAutoRefresh(func, events) {\n  for (var i = 0; i < _autoRefresh.length; i += 3) {\n    (!events || ~events.indexOf(_autoRefresh[i + 1])) && func(_autoRefresh[i], _autoRefresh[i + 1], _autoRefresh[i + 2]);\n  }\n},\n    _isString = function _isString(value) {\n  return typeof value === \"string\";\n},\n    _isFunction = function _isFunction(value) {\n  return typeof value === \"function\";\n},\n    _isNumber = function _isNumber(value) {\n  return typeof value === \"number\";\n},\n    _isObject = function _isObject(value) {\n  return typeof value === \"object\";\n},\n    _endAnimation = function _endAnimation(animation, reversed, pause) {\n  return animation && animation.progress(reversed ? 0 : 1) && pause && animation.pause();\n},\n    _callback = function _callback(self, func) {\n  if (self.enabled) {\n    var result = self._ctx ? self._ctx.add(function () {\n      return func(self);\n    }) : func(self);\n    result && result.totalTime && (self.callbackAnimation = result);\n  }\n},\n    _abs = Math.abs,\n    _left = \"left\",\n    _top = \"top\",\n    _right = \"right\",\n    _bottom = \"bottom\",\n    _width = \"width\",\n    _height = \"height\",\n    _Right = \"Right\",\n    _Left = \"Left\",\n    _Top = \"Top\",\n    _Bottom = \"Bottom\",\n    _padding = \"padding\",\n    _margin = \"margin\",\n    _Width = \"Width\",\n    _Height = \"Height\",\n    _px = \"px\",\n    _getComputedStyle = function _getComputedStyle(element) {\n  return _win.getComputedStyle(element);\n},\n    _makePositionable = function _makePositionable(element) {\n  // if the element already has position: absolute or fixed, leave that, otherwise make it position: relative\n  var position = _getComputedStyle(element).position;\n\n  element.style.position = position === \"absolute\" || position === \"fixed\" ? position : \"relative\";\n},\n    _setDefaults = function _setDefaults(obj, defaults) {\n  for (var p in defaults) {\n    p in obj || (obj[p] = defaults[p]);\n  }\n\n  return obj;\n},\n    _getBounds = function _getBounds(element, withoutTransforms) {\n  var tween = withoutTransforms && _getComputedStyle(element)[_transformProp] !== \"matrix(1, 0, 0, 1, 0, 0)\" && gsap.to(element, {\n    x: 0,\n    y: 0,\n    xPercent: 0,\n    yPercent: 0,\n    rotation: 0,\n    rotationX: 0,\n    rotationY: 0,\n    scale: 1,\n    skewX: 0,\n    skewY: 0\n  }).progress(1),\n      bounds = element.getBoundingClientRect();\n  tween && tween.progress(0).kill();\n  return bounds;\n},\n    _getSize = function _getSize(element, _ref3) {\n  var d2 = _ref3.d2;\n  return element[\"offset\" + d2] || element[\"client\" + d2] || 0;\n},\n    _getLabelRatioArray = function _getLabelRatioArray(timeline) {\n  var a = [],\n      labels = timeline.labels,\n      duration = timeline.duration(),\n      p;\n\n  for (p in labels) {\n    a.push(labels[p] / duration);\n  }\n\n  return a;\n},\n    _getClosestLabel = function _getClosestLabel(animation) {\n  return function (value) {\n    return gsap.utils.snap(_getLabelRatioArray(animation), value);\n  };\n},\n    _snapDirectional = function _snapDirectional(snapIncrementOrArray) {\n  var snap = gsap.utils.snap(snapIncrementOrArray),\n      a = Array.isArray(snapIncrementOrArray) && snapIncrementOrArray.slice(0).sort(function (a, b) {\n    return a - b;\n  });\n  return a ? function (value, direction, threshold) {\n    if (threshold === void 0) {\n      threshold = 1e-3;\n    }\n\n    var i;\n\n    if (!direction) {\n      return snap(value);\n    }\n\n    if (direction > 0) {\n      value -= threshold; // to avoid rounding errors. If we're too strict, it might snap forward, then immediately again, and again.\n\n      for (i = 0; i < a.length; i++) {\n        if (a[i] >= value) {\n          return a[i];\n        }\n      }\n\n      return a[i - 1];\n    } else {\n      i = a.length;\n      value += threshold;\n\n      while (i--) {\n        if (a[i] <= value) {\n          return a[i];\n        }\n      }\n    }\n\n    return a[0];\n  } : function (value, direction, threshold) {\n    if (threshold === void 0) {\n      threshold = 1e-3;\n    }\n\n    var snapped = snap(value);\n    return !direction || Math.abs(snapped - value) < threshold || snapped - value < 0 === direction < 0 ? snapped : snap(direction < 0 ? value - snapIncrementOrArray : value + snapIncrementOrArray);\n  };\n},\n    _getLabelAtDirection = function _getLabelAtDirection(timeline) {\n  return function (value, st) {\n    return _snapDirectional(_getLabelRatioArray(timeline))(value, st.direction);\n  };\n},\n    _multiListener = function _multiListener(func, element, types, callback) {\n  return types.split(\",\").forEach(function (type) {\n    return func(element, type, callback);\n  });\n},\n    _addListener = function _addListener(element, type, func, nonPassive, capture) {\n  return element.addEventListener(type, func, {\n    passive: !nonPassive,\n    capture: !!capture\n  });\n},\n    _removeListener = function _removeListener(element, type, func, capture) {\n  return element.removeEventListener(type, func, !!capture);\n},\n    _wheelListener = function _wheelListener(func, el, scrollFunc) {\n  scrollFunc = scrollFunc && scrollFunc.wheelHandler;\n\n  if (scrollFunc) {\n    func(el, \"wheel\", scrollFunc);\n    func(el, \"touchmove\", scrollFunc);\n  }\n},\n    _markerDefaults = {\n  startColor: \"green\",\n  endColor: \"red\",\n  indent: 0,\n  fontSize: \"16px\",\n  fontWeight: \"normal\"\n},\n    _defaults = {\n  toggleActions: \"play\",\n  anticipatePin: 0\n},\n    _keywords = {\n  top: 0,\n  left: 0,\n  center: 0.5,\n  bottom: 1,\n  right: 1\n},\n    _offsetToPx = function _offsetToPx(value, size) {\n  if (_isString(value)) {\n    var eqIndex = value.indexOf(\"=\"),\n        relative = ~eqIndex ? +(value.charAt(eqIndex - 1) + 1) * parseFloat(value.substr(eqIndex + 1)) : 0;\n\n    if (~eqIndex) {\n      value.indexOf(\"%\") > eqIndex && (relative *= size / 100);\n      value = value.substr(0, eqIndex - 1);\n    }\n\n    value = relative + (value in _keywords ? _keywords[value] * size : ~value.indexOf(\"%\") ? parseFloat(value) * size / 100 : parseFloat(value) || 0);\n  }\n\n  return value;\n},\n    _createMarker = function _createMarker(type, name, container, direction, _ref4, offset, matchWidthEl, containerAnimation) {\n  var startColor = _ref4.startColor,\n      endColor = _ref4.endColor,\n      fontSize = _ref4.fontSize,\n      indent = _ref4.indent,\n      fontWeight = _ref4.fontWeight;\n\n  var e = _doc.createElement(\"div\"),\n      useFixedPosition = _isViewport(container) || _getProxyProp(container, \"pinType\") === \"fixed\",\n      isScroller = type.indexOf(\"scroller\") !== -1,\n      parent = useFixedPosition ? _body : container,\n      isStart = type.indexOf(\"start\") !== -1,\n      color = isStart ? startColor : endColor,\n      css = \"border-color:\" + color + \";font-size:\" + fontSize + \";color:\" + color + \";font-weight:\" + fontWeight + \";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;\";\n\n  css += \"position:\" + ((isScroller || containerAnimation) && useFixedPosition ? \"fixed;\" : \"absolute;\");\n  (isScroller || containerAnimation || !useFixedPosition) && (css += (direction === _vertical ? _right : _bottom) + \":\" + (offset + parseFloat(indent)) + \"px;\");\n  matchWidthEl && (css += \"box-sizing:border-box;text-align:left;width:\" + matchWidthEl.offsetWidth + \"px;\");\n  e._isStart = isStart;\n  e.setAttribute(\"class\", \"gsap-marker-\" + type + (name ? \" marker-\" + name : \"\"));\n  e.style.cssText = css;\n  e.innerText = name || name === 0 ? type + \"-\" + name : type;\n  parent.children[0] ? parent.insertBefore(e, parent.children[0]) : parent.appendChild(e);\n  e._offset = e[\"offset\" + direction.op.d2];\n\n  _positionMarker(e, 0, direction, isStart);\n\n  return e;\n},\n    _positionMarker = function _positionMarker(marker, start, direction, flipped) {\n  var vars = {\n    display: \"block\"\n  },\n      side = direction[flipped ? \"os2\" : \"p2\"],\n      oppositeSide = direction[flipped ? \"p2\" : \"os2\"];\n  marker._isFlipped = flipped;\n  vars[direction.a + \"Percent\"] = flipped ? -100 : 0;\n  vars[direction.a] = flipped ? \"1px\" : 0;\n  vars[\"border\" + side + _Width] = 1;\n  vars[\"border\" + oppositeSide + _Width] = 0;\n  vars[direction.p] = start + \"px\";\n  gsap.set(marker, vars);\n},\n    _triggers = [],\n    _ids = {},\n    _rafID,\n    _sync = function _sync() {\n  return _getTime() - _lastScrollTime > 34 && (_rafID || (_rafID = requestAnimationFrame(_updateAll)));\n},\n    _onScroll = function _onScroll() {\n  // previously, we tried to optimize performance by batching/deferring to the next requestAnimationFrame(), but discovered that Safari has a few bugs that make this unworkable (especially on iOS). See https://codepen.io/GreenSock/pen/16c435b12ef09c38125204818e7b45fc?editors=0010 and https://codepen.io/GreenSock/pen/JjOxYpQ/3dd65ccec5a60f1d862c355d84d14562?editors=0010 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503?editors=0010\n  if (!_normalizer || !_normalizer.isPressed || _normalizer.startX > _body.clientWidth) {\n    // if the user is dragging the scrollbar, allow it.\n    _scrollers.cache++;\n\n    if (_normalizer) {\n      _rafID || (_rafID = requestAnimationFrame(_updateAll));\n    } else {\n      _updateAll(); // Safari in particular (on desktop) NEEDS the immediate update rather than waiting for a requestAnimationFrame() whereas iOS seems to benefit from waiting for the requestAnimationFrame() tick, at least when normalizing. See https://codepen.io/GreenSock/pen/qBYozqO?editors=0110\n\n    }\n\n    _lastScrollTime || _dispatch(\"scrollStart\");\n    _lastScrollTime = _getTime();\n  }\n},\n    _setBaseDimensions = function _setBaseDimensions() {\n  _baseScreenWidth = _win.innerWidth;\n  _baseScreenHeight = _win.innerHeight;\n},\n    _onResize = function _onResize(force) {\n  _scrollers.cache++;\n  (force === true || !_refreshing && !_ignoreResize && !_doc.fullscreenElement && !_doc.webkitFullscreenElement && (!_ignoreMobileResize || _baseScreenWidth !== _win.innerWidth || Math.abs(_win.innerHeight - _baseScreenHeight) > _win.innerHeight * 0.25)) && _resizeDelay.restart(true);\n},\n    // ignore resizes triggered by refresh()\n_listeners = {},\n    _emptyArray = [],\n    _softRefresh = function _softRefresh() {\n  return _removeListener(ScrollTrigger, \"scrollEnd\", _softRefresh) || _refreshAll(true);\n},\n    _dispatch = function _dispatch(type) {\n  return _listeners[type] && _listeners[type].map(function (f) {\n    return f();\n  }) || _emptyArray;\n},\n    _savedStyles = [],\n    // when ScrollTrigger.saveStyles() is called, the inline styles are recorded in this Array in a sequential format like [element, cssText, gsCache, media]. This keeps it very memory-efficient and fast to iterate through.\n_revertRecorded = function _revertRecorded(media) {\n  for (var i = 0; i < _savedStyles.length; i += 5) {\n    if (!media || _savedStyles[i + 4] && _savedStyles[i + 4].query === media) {\n      _savedStyles[i].style.cssText = _savedStyles[i + 1];\n      _savedStyles[i].getBBox && _savedStyles[i].setAttribute(\"transform\", _savedStyles[i + 2] || \"\");\n      _savedStyles[i + 3].uncache = 1;\n    }\n  }\n},\n    _revertAll = function _revertAll(kill, media) {\n  var trigger;\n\n  for (_i = 0; _i < _triggers.length; _i++) {\n    trigger = _triggers[_i];\n\n    if (trigger && (!media || trigger._ctx === media)) {\n      if (kill) {\n        trigger.kill(1);\n      } else {\n        trigger.revert(true, true);\n      }\n    }\n  }\n\n  _isReverted = true;\n  media && _revertRecorded(media);\n  media || _dispatch(\"revert\");\n},\n    _clearScrollMemory = function _clearScrollMemory(scrollRestoration, force) {\n  // zero-out all the recorded scroll positions. Don't use _triggers because if, for example, .matchMedia() is used to create some ScrollTriggers and then the user resizes and it removes ALL ScrollTriggers, and then go back to a size where there are ScrollTriggers, it would have kept the position(s) saved from the initial state.\n  _scrollers.cache++;\n  (force || !_refreshingAll) && _scrollers.forEach(function (obj) {\n    return _isFunction(obj) && obj.cacheID++ && (obj.rec = 0);\n  });\n  _isString(scrollRestoration) && (_win.history.scrollRestoration = _scrollRestoration = scrollRestoration);\n},\n    _refreshingAll,\n    _refreshID = 0,\n    _queueRefreshID,\n    _queueRefreshAll = function _queueRefreshAll() {\n  // we don't want to call _refreshAll() every time we create a new ScrollTrigger (for performance reasons) - it's better to batch them. Some frameworks dynamically load content and we can't rely on the window's \"load\" or \"DOMContentLoaded\" events to trigger it.\n  if (_queueRefreshID !== _refreshID) {\n    var id = _queueRefreshID = _refreshID;\n    requestAnimationFrame(function () {\n      return id === _refreshID && _refreshAll(true);\n    });\n  }\n},\n    _refresh100vh = function _refresh100vh() {\n  _body.appendChild(_div100vh);\n\n  _100vh = !_normalizer && _div100vh.offsetHeight || _win.innerHeight;\n\n  _body.removeChild(_div100vh);\n},\n    _hideAllMarkers = function _hideAllMarkers(hide) {\n  return _toArray(\".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end\").forEach(function (el) {\n    return el.style.display = hide ? \"none\" : \"block\";\n  });\n},\n    _refreshAll = function _refreshAll(force, skipRevert) {\n  _docEl = _doc.documentElement; // some frameworks like Astro may cache the <body> and replace it during routing, so we'll just re-record the _docEl and _body for safety (otherwise, the markers may not get added properly).\n\n  _body = _doc.body;\n  _root = [_win, _doc, _docEl, _body];\n\n  if (_lastScrollTime && !force && !_isReverted) {\n    _addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\n    return;\n  }\n\n  _refresh100vh();\n\n  _refreshingAll = ScrollTrigger.isRefreshing = true;\n\n  _scrollers.forEach(function (obj) {\n    return _isFunction(obj) && ++obj.cacheID && (obj.rec = obj());\n  }); // force the clearing of the cache because some browsers take a little while to dispatch the \"scroll\" event and the user may have changed the scroll position and then called ScrollTrigger.refresh() right away\n\n\n  var refreshInits = _dispatch(\"refreshInit\");\n\n  _sort && ScrollTrigger.sort();\n  skipRevert || _revertAll();\n\n  _scrollers.forEach(function (obj) {\n    if (_isFunction(obj)) {\n      obj.smooth && (obj.target.style.scrollBehavior = \"auto\"); // smooth scrolling interferes\n\n      obj(0);\n    }\n  });\n\n  _triggers.slice(0).forEach(function (t) {\n    return t.refresh();\n  }); // don't loop with _i because during a refresh() someone could call ScrollTrigger.update() which would iterate through _i resulting in a skip.\n\n\n  _isReverted = false;\n\n  _triggers.forEach(function (t) {\n    // nested pins (pinnedContainer) with pinSpacing may expand the container, so we must accommodate that here.\n    if (t._subPinOffset && t.pin) {\n      var prop = t.vars.horizontal ? \"offsetWidth\" : \"offsetHeight\",\n          original = t.pin[prop];\n      t.revert(true, 1);\n      t.adjustPinSpacing(t.pin[prop] - original);\n      t.refresh();\n    }\n  });\n\n  _clampingMax = 1; // pinSpacing might be propping a page open, thus when we .setPositions() to clamp a ScrollTrigger's end we should leave the pinSpacing alone. That's what this flag is for.\n\n  _hideAllMarkers(true);\n\n  _triggers.forEach(function (t) {\n    // the scroller's max scroll position may change after all the ScrollTriggers refreshed (like pinning could push it down), so we need to loop back and correct any with end: \"max\". Same for anything with a clamped end\n    var max = _maxScroll(t.scroller, t._dir),\n        endClamp = t.vars.end === \"max\" || t._endClamp && t.end > max,\n        startClamp = t._startClamp && t.start >= max;\n\n    (endClamp || startClamp) && t.setPositions(startClamp ? max - 1 : t.start, endClamp ? Math.max(startClamp ? max : t.start + 1, max) : t.end, true);\n  });\n\n  _hideAllMarkers(false);\n\n  _clampingMax = 0;\n  refreshInits.forEach(function (result) {\n    return result && result.render && result.render(-1);\n  }); // if the onRefreshInit() returns an animation (typically a gsap.set()), revert it. This makes it easy to put things in a certain spot before refreshing for measurement purposes, and then put things back.\n\n  _scrollers.forEach(function (obj) {\n    if (_isFunction(obj)) {\n      obj.smooth && requestAnimationFrame(function () {\n        return obj.target.style.scrollBehavior = \"smooth\";\n      });\n      obj.rec && obj(obj.rec);\n    }\n  });\n\n  _clearScrollMemory(_scrollRestoration, 1);\n\n  _resizeDelay.pause();\n\n  _refreshID++;\n  _refreshingAll = 2;\n\n  _updateAll(2);\n\n  _triggers.forEach(function (t) {\n    return _isFunction(t.vars.onRefresh) && t.vars.onRefresh(t);\n  });\n\n  _refreshingAll = ScrollTrigger.isRefreshing = false;\n\n  _dispatch(\"refresh\");\n},\n    _lastScroll = 0,\n    _direction = 1,\n    _primary,\n    _updateAll = function _updateAll(force) {\n  if (force === 2 || !_refreshingAll && !_isReverted) {\n    // _isReverted could be true if, for example, a matchMedia() is in the process of executing. We don't want to update during the time everything is reverted.\n    ScrollTrigger.isUpdating = true;\n    _primary && _primary.update(0); // ScrollSmoother uses refreshPriority -9999 to become the primary that gets updated before all others because it affects the scroll position.\n\n    var l = _triggers.length,\n        time = _getTime(),\n        recordVelocity = time - _time1 >= 50,\n        scroll = l && _triggers[0].scroll();\n\n    _direction = _lastScroll > scroll ? -1 : 1;\n    _refreshingAll || (_lastScroll = scroll);\n\n    if (recordVelocity) {\n      if (_lastScrollTime && !_pointerIsDown && time - _lastScrollTime > 200) {\n        _lastScrollTime = 0;\n\n        _dispatch(\"scrollEnd\");\n      }\n\n      _time2 = _time1;\n      _time1 = time;\n    }\n\n    if (_direction < 0) {\n      _i = l;\n\n      while (_i-- > 0) {\n        _triggers[_i] && _triggers[_i].update(0, recordVelocity);\n      }\n\n      _direction = 1;\n    } else {\n      for (_i = 0; _i < l; _i++) {\n        _triggers[_i] && _triggers[_i].update(0, recordVelocity);\n      }\n    }\n\n    ScrollTrigger.isUpdating = false;\n  }\n\n  _rafID = 0;\n},\n    _propNamesToCopy = [_left, _top, _bottom, _right, _margin + _Bottom, _margin + _Right, _margin + _Top, _margin + _Left, \"display\", \"flexShrink\", \"float\", \"zIndex\", \"gridColumnStart\", \"gridColumnEnd\", \"gridRowStart\", \"gridRowEnd\", \"gridArea\", \"justifySelf\", \"alignSelf\", \"placeSelf\", \"order\"],\n    _stateProps = _propNamesToCopy.concat([_width, _height, \"boxSizing\", \"max\" + _Width, \"max\" + _Height, \"position\", _margin, _padding, _padding + _Top, _padding + _Right, _padding + _Bottom, _padding + _Left]),\n    _swapPinOut = function _swapPinOut(pin, spacer, state) {\n  _setState(state);\n\n  var cache = pin._gsap;\n\n  if (cache.spacerIsNative) {\n    _setState(cache.spacerState);\n  } else if (pin._gsap.swappedIn) {\n    var parent = spacer.parentNode;\n\n    if (parent) {\n      parent.insertBefore(pin, spacer);\n      parent.removeChild(spacer);\n    }\n  }\n\n  pin._gsap.swappedIn = false;\n},\n    _swapPinIn = function _swapPinIn(pin, spacer, cs, spacerState) {\n  if (!pin._gsap.swappedIn) {\n    var i = _propNamesToCopy.length,\n        spacerStyle = spacer.style,\n        pinStyle = pin.style,\n        p;\n\n    while (i--) {\n      p = _propNamesToCopy[i];\n      spacerStyle[p] = cs[p];\n    }\n\n    spacerStyle.position = cs.position === \"absolute\" ? \"absolute\" : \"relative\";\n    cs.display === \"inline\" && (spacerStyle.display = \"inline-block\");\n    pinStyle[_bottom] = pinStyle[_right] = \"auto\";\n    spacerStyle.flexBasis = cs.flexBasis || \"auto\";\n    spacerStyle.overflow = \"visible\";\n    spacerStyle.boxSizing = \"border-box\";\n    spacerStyle[_width] = _getSize(pin, _horizontal) + _px;\n    spacerStyle[_height] = _getSize(pin, _vertical) + _px;\n    spacerStyle[_padding] = pinStyle[_margin] = pinStyle[_top] = pinStyle[_left] = \"0\";\n\n    _setState(spacerState);\n\n    pinStyle[_width] = pinStyle[\"max\" + _Width] = cs[_width];\n    pinStyle[_height] = pinStyle[\"max\" + _Height] = cs[_height];\n    pinStyle[_padding] = cs[_padding];\n\n    if (pin.parentNode !== spacer) {\n      pin.parentNode.insertBefore(spacer, pin);\n      spacer.appendChild(pin);\n    }\n\n    pin._gsap.swappedIn = true;\n  }\n},\n    _capsExp = /([A-Z])/g,\n    _setState = function _setState(state) {\n  if (state) {\n    var style = state.t.style,\n        l = state.length,\n        i = 0,\n        p,\n        value;\n    (state.t._gsap || gsap.core.getCache(state.t)).uncache = 1; // otherwise transforms may be off\n\n    for (; i < l; i += 2) {\n      value = state[i + 1];\n      p = state[i];\n\n      if (value) {\n        style[p] = value;\n      } else if (style[p]) {\n        style.removeProperty(p.replace(_capsExp, \"-$1\").toLowerCase());\n      }\n    }\n  }\n},\n    _getState = function _getState(element) {\n  // returns an Array with alternating values like [property, value, property, value] and a \"t\" property pointing to the target (element). Makes it fast and cheap.\n  var l = _stateProps.length,\n      style = element.style,\n      state = [],\n      i = 0;\n\n  for (; i < l; i++) {\n    state.push(_stateProps[i], style[_stateProps[i]]);\n  }\n\n  state.t = element;\n  return state;\n},\n    _copyState = function _copyState(state, override, omitOffsets) {\n  var result = [],\n      l = state.length,\n      i = omitOffsets ? 8 : 0,\n      // skip top, left, right, bottom if omitOffsets is true\n  p;\n\n  for (; i < l; i += 2) {\n    p = state[i];\n    result.push(p, p in override ? override[p] : state[i + 1]);\n  }\n\n  result.t = state.t;\n  return result;\n},\n    _winOffsets = {\n  left: 0,\n  top: 0\n},\n    // // potential future feature (?) Allow users to calculate where a trigger hits (scroll position) like getScrollPosition(\"#id\", \"top bottom\")\n// _getScrollPosition = (trigger, position, {scroller, containerAnimation, horizontal}) => {\n// \tscroller = _getTarget(scroller || _win);\n// \tlet direction = horizontal ? _horizontal : _vertical,\n// \t\tisViewport = _isViewport(scroller);\n// \t_getSizeFunc(scroller, isViewport, direction);\n// \treturn _parsePosition(position, _getTarget(trigger), _getSizeFunc(scroller, isViewport, direction)(), direction, _getScrollFunc(scroller, direction)(), 0, 0, 0, _getOffsetsFunc(scroller, isViewport)(), isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0, 0, containerAnimation ? containerAnimation.duration() : _maxScroll(scroller), containerAnimation);\n// },\n_parsePosition = function _parsePosition(value, trigger, scrollerSize, direction, scroll, marker, markerScroller, self, scrollerBounds, borderWidth, useFixedPosition, scrollerMax, containerAnimation, clampZeroProp) {\n  _isFunction(value) && (value = value(self));\n\n  if (_isString(value) && value.substr(0, 3) === \"max\") {\n    value = scrollerMax + (value.charAt(4) === \"=\" ? _offsetToPx(\"0\" + value.substr(3), scrollerSize) : 0);\n  }\n\n  var time = containerAnimation ? containerAnimation.time() : 0,\n      p1,\n      p2,\n      element;\n  containerAnimation && containerAnimation.seek(0);\n  isNaN(value) || (value = +value); // convert a string number like \"45\" to an actual number\n\n  if (!_isNumber(value)) {\n    _isFunction(trigger) && (trigger = trigger(self));\n    var offsets = (value || \"0\").split(\" \"),\n        bounds,\n        localOffset,\n        globalOffset,\n        display;\n    element = _getTarget(trigger, self) || _body;\n    bounds = _getBounds(element) || {};\n\n    if ((!bounds || !bounds.left && !bounds.top) && _getComputedStyle(element).display === \"none\") {\n      // if display is \"none\", it won't report getBoundingClientRect() properly\n      display = element.style.display;\n      element.style.display = \"block\";\n      bounds = _getBounds(element);\n      display ? element.style.display = display : element.style.removeProperty(\"display\");\n    }\n\n    localOffset = _offsetToPx(offsets[0], bounds[direction.d]);\n    globalOffset = _offsetToPx(offsets[1] || \"0\", scrollerSize);\n    value = bounds[direction.p] - scrollerBounds[direction.p] - borderWidth + localOffset + scroll - globalOffset;\n    markerScroller && _positionMarker(markerScroller, globalOffset, direction, scrollerSize - globalOffset < 20 || markerScroller._isStart && globalOffset > 20);\n    scrollerSize -= scrollerSize - globalOffset; // adjust for the marker\n  } else {\n    containerAnimation && (value = gsap.utils.mapRange(containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, 0, scrollerMax, value));\n    markerScroller && _positionMarker(markerScroller, scrollerSize, direction, true);\n  }\n\n  if (clampZeroProp) {\n    self[clampZeroProp] = value || -0.001;\n    value < 0 && (value = 0);\n  }\n\n  if (marker) {\n    var position = value + scrollerSize,\n        isStart = marker._isStart;\n    p1 = \"scroll\" + direction.d2;\n\n    _positionMarker(marker, position, direction, isStart && position > 20 || !isStart && (useFixedPosition ? Math.max(_body[p1], _docEl[p1]) : marker.parentNode[p1]) <= position + 1);\n\n    if (useFixedPosition) {\n      scrollerBounds = _getBounds(markerScroller);\n      useFixedPosition && (marker.style[direction.op.p] = scrollerBounds[direction.op.p] - direction.op.m - marker._offset + _px);\n    }\n  }\n\n  if (containerAnimation && element) {\n    p1 = _getBounds(element);\n    containerAnimation.seek(scrollerMax);\n    p2 = _getBounds(element);\n    containerAnimation._caScrollDist = p1[direction.p] - p2[direction.p];\n    value = value / containerAnimation._caScrollDist * scrollerMax;\n  }\n\n  containerAnimation && containerAnimation.seek(time);\n  return containerAnimation ? value : Math.round(value);\n},\n    _prefixExp = /(webkit|moz|length|cssText|inset)/i,\n    _reparent = function _reparent(element, parent, top, left) {\n  if (element.parentNode !== parent) {\n    var style = element.style,\n        p,\n        cs;\n\n    if (parent === _body) {\n      element._stOrig = style.cssText; // record original inline styles so we can revert them later\n\n      cs = _getComputedStyle(element);\n\n      for (p in cs) {\n        // must copy all relevant styles to ensure that nothing changes visually when we reparent to the <body>. Skip the vendor prefixed ones.\n        if (!+p && !_prefixExp.test(p) && cs[p] && typeof style[p] === \"string\" && p !== \"0\") {\n          style[p] = cs[p];\n        }\n      }\n\n      style.top = top;\n      style.left = left;\n    } else {\n      style.cssText = element._stOrig;\n    }\n\n    gsap.core.getCache(element).uncache = 1;\n    parent.appendChild(element);\n  }\n},\n    _interruptionTracker = function _interruptionTracker(getValueFunc, initialValue, onInterrupt) {\n  var last1 = initialValue,\n      last2 = last1;\n  return function (value) {\n    var current = Math.round(getValueFunc()); // round because in some [very uncommon] Windows environments, scroll can get reported with decimals even though it was set without.\n\n    if (current !== last1 && current !== last2 && Math.abs(current - last1) > 3 && Math.abs(current - last2) > 3) {\n      // if the user scrolls, kill the tween. iOS Safari intermittently misreports the scroll position, it may be the most recently-set one or the one before that! When Safari is zoomed (CMD-+), it often misreports as 1 pixel off too! So if we set the scroll position to 125, for example, it'll actually report it as 124.\n      value = current;\n      onInterrupt && onInterrupt();\n    }\n\n    last2 = last1;\n    last1 = Math.round(value);\n    return last1;\n  };\n},\n    _shiftMarker = function _shiftMarker(marker, direction, value) {\n  var vars = {};\n  vars[direction.p] = \"+=\" + value;\n  gsap.set(marker, vars);\n},\n    // _mergeAnimations = animations => {\n// \tlet tl = gsap.timeline({smoothChildTiming: true}).startTime(Math.min(...animations.map(a => a.globalTime(0))));\n// \tanimations.forEach(a => {let time = a.totalTime(); tl.add(a); a.totalTime(time); });\n// \ttl.smoothChildTiming = false;\n// \treturn tl;\n// },\n// returns a function that can be used to tween the scroll position in the direction provided, and when doing so it'll add a .tween property to the FUNCTION itself, and remove it when the tween completes or gets killed. This gives us a way to have multiple ScrollTriggers use a central function for any given scroller and see if there's a scroll tween running (which would affect if/how things get updated)\n_getTweenCreator = function _getTweenCreator(scroller, direction) {\n  var getScroll = _getScrollFunc(scroller, direction),\n      prop = \"_scroll\" + direction.p2,\n      // add a tweenable property to the scroller that's a getter/setter function, like _scrollTop or _scrollLeft. This way, if someone does gsap.killTweensOf(scroller) it'll kill the scroll tween.\n  getTween = function getTween(scrollTo, vars, initialValue, change1, change2) {\n    var tween = getTween.tween,\n        onComplete = vars.onComplete,\n        modifiers = {};\n    initialValue = initialValue || getScroll();\n\n    var checkForInterruption = _interruptionTracker(getScroll, initialValue, function () {\n      tween.kill();\n      getTween.tween = 0;\n    });\n\n    change2 = change1 && change2 || 0; // if change1 is 0, we set that to the difference and ignore change2. Otherwise, there would be a compound effect.\n\n    change1 = change1 || scrollTo - initialValue;\n    tween && tween.kill();\n    vars[prop] = scrollTo;\n    vars.inherit = false;\n    vars.modifiers = modifiers;\n\n    modifiers[prop] = function () {\n      return checkForInterruption(initialValue + change1 * tween.ratio + change2 * tween.ratio * tween.ratio);\n    };\n\n    vars.onUpdate = function () {\n      _scrollers.cache++;\n      getTween.tween && _updateAll(); // if it was interrupted/killed, like in a context.revert(), don't force an updateAll()\n    };\n\n    vars.onComplete = function () {\n      getTween.tween = 0;\n      onComplete && onComplete.call(tween);\n    };\n\n    tween = getTween.tween = gsap.to(scroller, vars);\n    return tween;\n  };\n\n  scroller[prop] = getScroll;\n\n  getScroll.wheelHandler = function () {\n    return getTween.tween && getTween.tween.kill() && (getTween.tween = 0);\n  };\n\n  _addListener(scroller, \"wheel\", getScroll.wheelHandler); // Windows machines handle mousewheel scrolling in chunks (like \"3 lines per scroll\") meaning the typical strategy for cancelling the scroll isn't as sensitive. It's much more likely to match one of the previous 2 scroll event positions. So we kill any snapping as soon as there's a wheel event.\n\n\n  ScrollTrigger.isTouch && _addListener(scroller, \"touchmove\", getScroll.wheelHandler);\n  return getTween;\n};\n\nexport var ScrollTrigger = /*#__PURE__*/function () {\n  function ScrollTrigger(vars, animation) {\n    _coreInitted || ScrollTrigger.register(gsap) || console.warn(\"Please gsap.registerPlugin(ScrollTrigger)\");\n\n    _context(this);\n\n    this.init(vars, animation);\n  }\n\n  var _proto = ScrollTrigger.prototype;\n\n  _proto.init = function init(vars, animation) {\n    this.progress = this.start = 0;\n    this.vars && this.kill(true, true); // in case it's being initted again\n\n    if (!_enabled) {\n      this.update = this.refresh = this.kill = _passThrough;\n      return;\n    }\n\n    vars = _setDefaults(_isString(vars) || _isNumber(vars) || vars.nodeType ? {\n      trigger: vars\n    } : vars, _defaults);\n\n    var _vars = vars,\n        onUpdate = _vars.onUpdate,\n        toggleClass = _vars.toggleClass,\n        id = _vars.id,\n        onToggle = _vars.onToggle,\n        onRefresh = _vars.onRefresh,\n        scrub = _vars.scrub,\n        trigger = _vars.trigger,\n        pin = _vars.pin,\n        pinSpacing = _vars.pinSpacing,\n        invalidateOnRefresh = _vars.invalidateOnRefresh,\n        anticipatePin = _vars.anticipatePin,\n        onScrubComplete = _vars.onScrubComplete,\n        onSnapComplete = _vars.onSnapComplete,\n        once = _vars.once,\n        snap = _vars.snap,\n        pinReparent = _vars.pinReparent,\n        pinSpacer = _vars.pinSpacer,\n        containerAnimation = _vars.containerAnimation,\n        fastScrollEnd = _vars.fastScrollEnd,\n        preventOverlaps = _vars.preventOverlaps,\n        direction = vars.horizontal || vars.containerAnimation && vars.horizontal !== false ? _horizontal : _vertical,\n        isToggle = !scrub && scrub !== 0,\n        scroller = _getTarget(vars.scroller || _win),\n        scrollerCache = gsap.core.getCache(scroller),\n        isViewport = _isViewport(scroller),\n        useFixedPosition = (\"pinType\" in vars ? vars.pinType : _getProxyProp(scroller, \"pinType\") || isViewport && \"fixed\") === \"fixed\",\n        callbacks = [vars.onEnter, vars.onLeave, vars.onEnterBack, vars.onLeaveBack],\n        toggleActions = isToggle && vars.toggleActions.split(\" \"),\n        markers = \"markers\" in vars ? vars.markers : _defaults.markers,\n        borderWidth = isViewport ? 0 : parseFloat(_getComputedStyle(scroller)[\"border\" + direction.p2 + _Width]) || 0,\n        self = this,\n        onRefreshInit = vars.onRefreshInit && function () {\n      return vars.onRefreshInit(self);\n    },\n        getScrollerSize = _getSizeFunc(scroller, isViewport, direction),\n        getScrollerOffsets = _getOffsetsFunc(scroller, isViewport),\n        lastSnap = 0,\n        lastRefresh = 0,\n        prevProgress = 0,\n        scrollFunc = _getScrollFunc(scroller, direction),\n        tweenTo,\n        pinCache,\n        snapFunc,\n        scroll1,\n        scroll2,\n        start,\n        end,\n        markerStart,\n        markerEnd,\n        markerStartTrigger,\n        markerEndTrigger,\n        markerVars,\n        executingOnRefresh,\n        change,\n        pinOriginalState,\n        pinActiveState,\n        pinState,\n        spacer,\n        offset,\n        pinGetter,\n        pinSetter,\n        pinStart,\n        pinChange,\n        spacingStart,\n        spacerState,\n        markerStartSetter,\n        pinMoves,\n        markerEndSetter,\n        cs,\n        snap1,\n        snap2,\n        scrubTween,\n        scrubSmooth,\n        snapDurClamp,\n        snapDelayedCall,\n        prevScroll,\n        prevAnimProgress,\n        caMarkerSetter,\n        customRevertReturn; // for the sake of efficiency, _startClamp/_endClamp serve like a truthy value indicating that clamping was enabled on the start/end, and ALSO store the actual pre-clamped numeric value. We tap into that in ScrollSmoother for speed effects. So for example, if start=\"clamp(top bottom)\" results in a start of -100 naturally, it would get clamped to 0 but -100 would be stored in _startClamp.\n\n\n    self._startClamp = self._endClamp = false;\n    self._dir = direction;\n    anticipatePin *= 45;\n    self.scroller = scroller;\n    self.scroll = containerAnimation ? containerAnimation.time.bind(containerAnimation) : scrollFunc;\n    scroll1 = scrollFunc();\n    self.vars = vars;\n    animation = animation || vars.animation;\n\n    if (\"refreshPriority\" in vars) {\n      _sort = 1;\n      vars.refreshPriority === -9999 && (_primary = self); // used by ScrollSmoother\n    }\n\n    scrollerCache.tweenScroll = scrollerCache.tweenScroll || {\n      top: _getTweenCreator(scroller, _vertical),\n      left: _getTweenCreator(scroller, _horizontal)\n    };\n    self.tweenTo = tweenTo = scrollerCache.tweenScroll[direction.p];\n\n    self.scrubDuration = function (value) {\n      scrubSmooth = _isNumber(value) && value;\n\n      if (!scrubSmooth) {\n        scrubTween && scrubTween.progress(1).kill();\n        scrubTween = 0;\n      } else {\n        scrubTween ? scrubTween.duration(value) : scrubTween = gsap.to(animation, {\n          ease: \"expo\",\n          totalProgress: \"+=0\",\n          inherit: false,\n          duration: scrubSmooth,\n          paused: true,\n          onComplete: function onComplete() {\n            return onScrubComplete && onScrubComplete(self);\n          }\n        });\n      }\n    };\n\n    if (animation) {\n      animation.vars.lazy = false;\n      animation._initted && !self.isReverted || animation.vars.immediateRender !== false && vars.immediateRender !== false && animation.duration() && animation.render(0, true, true); // special case: if this ScrollTrigger gets re-initted, a from() tween with a stagger could get initted initially and then reverted on the re-init which means it'll need to get rendered again here to properly display things. Otherwise, See https://gsap.com/forums/topic/36777-scrollsmoother-splittext-nextjs/ and https://codepen.io/GreenSock/pen/eYPyPpd?editors=0010\n\n      self.animation = animation.pause();\n      animation.scrollTrigger = self;\n      self.scrubDuration(scrub);\n      snap1 = 0;\n      id || (id = animation.vars.id);\n    }\n\n    if (snap) {\n      // TODO: potential idea: use legitimate CSS scroll snapping by pushing invisible elements into the DOM that serve as snap positions, and toggle the document.scrollingElement.style.scrollSnapType onToggle. See https://codepen.io/GreenSock/pen/JjLrgWM for a quick proof of concept.\n      if (!_isObject(snap) || snap.push) {\n        snap = {\n          snapTo: snap\n        };\n      }\n\n      \"scrollBehavior\" in _body.style && gsap.set(isViewport ? [_body, _docEl] : scroller, {\n        scrollBehavior: \"auto\"\n      }); // smooth scrolling doesn't work with snap.\n\n      _scrollers.forEach(function (o) {\n        return _isFunction(o) && o.target === (isViewport ? _doc.scrollingElement || _docEl : scroller) && (o.smooth = false);\n      }); // note: set smooth to false on both the vertical and horizontal scroll getters/setters\n\n\n      snapFunc = _isFunction(snap.snapTo) ? snap.snapTo : snap.snapTo === \"labels\" ? _getClosestLabel(animation) : snap.snapTo === \"labelsDirectional\" ? _getLabelAtDirection(animation) : snap.directional !== false ? function (value, st) {\n        return _snapDirectional(snap.snapTo)(value, _getTime() - lastRefresh < 500 ? 0 : st.direction);\n      } : gsap.utils.snap(snap.snapTo);\n      snapDurClamp = snap.duration || {\n        min: 0.1,\n        max: 2\n      };\n      snapDurClamp = _isObject(snapDurClamp) ? _clamp(snapDurClamp.min, snapDurClamp.max) : _clamp(snapDurClamp, snapDurClamp);\n      snapDelayedCall = gsap.delayedCall(snap.delay || scrubSmooth / 2 || 0.1, function () {\n        var scroll = scrollFunc(),\n            refreshedRecently = _getTime() - lastRefresh < 500,\n            tween = tweenTo.tween;\n\n        if ((refreshedRecently || Math.abs(self.getVelocity()) < 10) && !tween && !_pointerIsDown && lastSnap !== scroll) {\n          var progress = (scroll - start) / change,\n              totalProgress = animation && !isToggle ? animation.totalProgress() : progress,\n              velocity = refreshedRecently ? 0 : (totalProgress - snap2) / (_getTime() - _time2) * 1000 || 0,\n              change1 = gsap.utils.clamp(-progress, 1 - progress, _abs(velocity / 2) * velocity / 0.185),\n              naturalEnd = progress + (snap.inertia === false ? 0 : change1),\n              endValue,\n              endScroll,\n              _snap = snap,\n              onStart = _snap.onStart,\n              _onInterrupt = _snap.onInterrupt,\n              _onComplete = _snap.onComplete;\n          endValue = snapFunc(naturalEnd, self);\n          _isNumber(endValue) || (endValue = naturalEnd); // in case the function didn't return a number, fall back to using the naturalEnd\n\n          endScroll = Math.max(0, Math.round(start + endValue * change));\n\n          if (scroll <= end && scroll >= start && endScroll !== scroll) {\n            if (tween && !tween._initted && tween.data <= _abs(endScroll - scroll)) {\n              // there's an overlapping snap! So we must figure out which one is closer and let that tween live.\n              return;\n            }\n\n            if (snap.inertia === false) {\n              change1 = endValue - progress;\n            }\n\n            tweenTo(endScroll, {\n              duration: snapDurClamp(_abs(Math.max(_abs(naturalEnd - totalProgress), _abs(endValue - totalProgress)) * 0.185 / velocity / 0.05 || 0)),\n              ease: snap.ease || \"power3\",\n              data: _abs(endScroll - scroll),\n              // record the distance so that if another snap tween occurs (conflict) we can prioritize the closest snap.\n              onInterrupt: function onInterrupt() {\n                return snapDelayedCall.restart(true) && _onInterrupt && _onInterrupt(self);\n              },\n              onComplete: function onComplete() {\n                self.update();\n                lastSnap = scrollFunc();\n\n                if (animation && !isToggle) {\n                  // the resolution of the scrollbar is limited, so we should correct the scrubbed animation's playhead at the end to match EXACTLY where it was supposed to snap\n                  scrubTween ? scrubTween.resetTo(\"totalProgress\", endValue, animation._tTime / animation._tDur) : animation.progress(endValue);\n                }\n\n                snap1 = snap2 = animation && !isToggle ? animation.totalProgress() : self.progress;\n                onSnapComplete && onSnapComplete(self);\n                _onComplete && _onComplete(self);\n              }\n            }, scroll, change1 * change, endScroll - scroll - change1 * change);\n            onStart && onStart(self, tweenTo.tween);\n          }\n        } else if (self.isActive && lastSnap !== scroll) {\n          snapDelayedCall.restart(true);\n        }\n      }).pause();\n    }\n\n    id && (_ids[id] = self);\n    trigger = self.trigger = _getTarget(trigger || pin !== true && pin); // if a trigger has some kind of scroll-related effect applied that could contaminate the \"y\" or \"x\" position (like a ScrollSmoother effect), we needed a way to temporarily revert it, so we use the stRevert property of the gsCache. It can return another function that we'll call at the end so it can return to its normal state.\n\n    customRevertReturn = trigger && trigger._gsap && trigger._gsap.stRevert;\n    customRevertReturn && (customRevertReturn = customRevertReturn(self));\n    pin = pin === true ? trigger : _getTarget(pin);\n    _isString(toggleClass) && (toggleClass = {\n      targets: trigger,\n      className: toggleClass\n    });\n\n    if (pin) {\n      pinSpacing === false || pinSpacing === _margin || (pinSpacing = !pinSpacing && pin.parentNode && pin.parentNode.style && _getComputedStyle(pin.parentNode).display === \"flex\" ? false : _padding); // if the parent is display: flex, don't apply pinSpacing by default. We should check that pin.parentNode is an element (not shadow dom window)\n\n      self.pin = pin;\n      pinCache = gsap.core.getCache(pin);\n\n      if (!pinCache.spacer) {\n        // record the spacer and pinOriginalState on the cache in case someone tries pinning the same element with MULTIPLE ScrollTriggers - we don't want to have multiple spacers or record the \"original\" pin state after it has already been affected by another ScrollTrigger.\n        if (pinSpacer) {\n          pinSpacer = _getTarget(pinSpacer);\n          pinSpacer && !pinSpacer.nodeType && (pinSpacer = pinSpacer.current || pinSpacer.nativeElement); // for React & Angular\n\n          pinCache.spacerIsNative = !!pinSpacer;\n          pinSpacer && (pinCache.spacerState = _getState(pinSpacer));\n        }\n\n        pinCache.spacer = spacer = pinSpacer || _doc.createElement(\"div\");\n        spacer.classList.add(\"pin-spacer\");\n        id && spacer.classList.add(\"pin-spacer-\" + id);\n        pinCache.pinState = pinOriginalState = _getState(pin);\n      } else {\n        pinOriginalState = pinCache.pinState;\n      }\n\n      vars.force3D !== false && gsap.set(pin, {\n        force3D: true\n      });\n      self.spacer = spacer = pinCache.spacer;\n      cs = _getComputedStyle(pin);\n      spacingStart = cs[pinSpacing + direction.os2];\n      pinGetter = gsap.getProperty(pin);\n      pinSetter = gsap.quickSetter(pin, direction.a, _px); // pin.firstChild && !_maxScroll(pin, direction) && (pin.style.overflow = \"hidden\"); // protects from collapsing margins, but can have unintended consequences as demonstrated here: https://codepen.io/GreenSock/pen/1e42c7a73bfa409d2cf1e184e7a4248d so it was removed in favor of just telling people to set up their CSS to avoid the collapsing margins (overflow: hidden | auto is just one option. Another is border-top: 1px solid transparent).\n\n      _swapPinIn(pin, spacer, cs);\n\n      pinState = _getState(pin);\n    }\n\n    if (markers) {\n      markerVars = _isObject(markers) ? _setDefaults(markers, _markerDefaults) : _markerDefaults;\n      markerStartTrigger = _createMarker(\"scroller-start\", id, scroller, direction, markerVars, 0);\n      markerEndTrigger = _createMarker(\"scroller-end\", id, scroller, direction, markerVars, 0, markerStartTrigger);\n      offset = markerStartTrigger[\"offset\" + direction.op.d2];\n\n      var content = _getTarget(_getProxyProp(scroller, \"content\") || scroller);\n\n      markerStart = this.markerStart = _createMarker(\"start\", id, content, direction, markerVars, offset, 0, containerAnimation);\n      markerEnd = this.markerEnd = _createMarker(\"end\", id, content, direction, markerVars, offset, 0, containerAnimation);\n      containerAnimation && (caMarkerSetter = gsap.quickSetter([markerStart, markerEnd], direction.a, _px));\n\n      if (!useFixedPosition && !(_proxies.length && _getProxyProp(scroller, \"fixedMarkers\") === true)) {\n        _makePositionable(isViewport ? _body : scroller);\n\n        gsap.set([markerStartTrigger, markerEndTrigger], {\n          force3D: true\n        });\n        markerStartSetter = gsap.quickSetter(markerStartTrigger, direction.a, _px);\n        markerEndSetter = gsap.quickSetter(markerEndTrigger, direction.a, _px);\n      }\n    }\n\n    if (containerAnimation) {\n      var oldOnUpdate = containerAnimation.vars.onUpdate,\n          oldParams = containerAnimation.vars.onUpdateParams;\n      containerAnimation.eventCallback(\"onUpdate\", function () {\n        self.update(0, 0, 1);\n        oldOnUpdate && oldOnUpdate.apply(containerAnimation, oldParams || []);\n      });\n    }\n\n    self.previous = function () {\n      return _triggers[_triggers.indexOf(self) - 1];\n    };\n\n    self.next = function () {\n      return _triggers[_triggers.indexOf(self) + 1];\n    };\n\n    self.revert = function (revert, temp) {\n      if (!temp) {\n        return self.kill(true);\n      } // for compatibility with gsap.context() and gsap.matchMedia() which call revert()\n\n\n      var r = revert !== false || !self.enabled,\n          prevRefreshing = _refreshing;\n\n      if (r !== self.isReverted) {\n        if (r) {\n          prevScroll = Math.max(scrollFunc(), self.scroll.rec || 0); // record the scroll so we can revert later (repositioning/pinning things can affect scroll position). In the static refresh() method, we first record all the scroll positions as a reference.\n\n          prevProgress = self.progress;\n          prevAnimProgress = animation && animation.progress();\n        }\n\n        markerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(function (m) {\n          return m.style.display = r ? \"none\" : \"block\";\n        });\n\n        if (r) {\n          _refreshing = self;\n          self.update(r); // make sure the pin is back in its original position so that all the measurements are correct. do this BEFORE swapping the pin out\n        }\n\n        if (pin && (!pinReparent || !self.isActive)) {\n          if (r) {\n            _swapPinOut(pin, spacer, pinOriginalState);\n          } else {\n            _swapPinIn(pin, spacer, _getComputedStyle(pin), spacerState);\n          }\n        }\n\n        r || self.update(r); // when we're restoring, the update should run AFTER swapping the pin into its pin-spacer.\n\n        _refreshing = prevRefreshing; // restore. We set it to true during the update() so that things fire properly in there.\n\n        self.isReverted = r;\n      }\n    };\n\n    self.refresh = function (soft, force, position, pinOffset) {\n      // position is typically only defined if it's coming from setPositions() - it's a way to skip the normal parsing. pinOffset is also only from setPositions() and is mostly related to fancy stuff we need to do in ScrollSmoother with effects\n      if ((_refreshing || !self.enabled) && !force) {\n        return;\n      }\n\n      if (pin && soft && _lastScrollTime) {\n        _addListener(ScrollTrigger, \"scrollEnd\", _softRefresh);\n\n        return;\n      }\n\n      !_refreshingAll && onRefreshInit && onRefreshInit(self);\n      _refreshing = self;\n\n      if (tweenTo.tween && !position) {\n        // we skip this if a position is passed in because typically that's from .setPositions() and it's best to allow in-progress snapping to continue.\n        tweenTo.tween.kill();\n        tweenTo.tween = 0;\n      }\n\n      scrubTween && scrubTween.pause();\n\n      if (invalidateOnRefresh && animation) {\n        animation.revert({\n          kill: false\n        }).invalidate();\n        animation.getChildren && animation.getChildren(true, true, false).forEach(function (t) {\n          return t.vars.immediateRender && t.render(0, true, true);\n        }); // any from() or fromTo() tweens inside a timeline should render immediately (well, unless they have immediateRender: false)\n      }\n\n      self.isReverted || self.revert(true, true);\n      self._subPinOffset = false; // we'll set this to true in the sub-pins if we find any\n\n      var size = getScrollerSize(),\n          scrollerBounds = getScrollerOffsets(),\n          max = containerAnimation ? containerAnimation.duration() : _maxScroll(scroller, direction),\n          isFirstRefresh = change <= 0.01 || !change,\n          offset = 0,\n          otherPinOffset = pinOffset || 0,\n          parsedEnd = _isObject(position) ? position.end : vars.end,\n          parsedEndTrigger = vars.endTrigger || trigger,\n          parsedStart = _isObject(position) ? position.start : vars.start || (vars.start === 0 || !trigger ? 0 : pin ? \"0 0\" : \"0 100%\"),\n          pinnedContainer = self.pinnedContainer = vars.pinnedContainer && _getTarget(vars.pinnedContainer, self),\n          triggerIndex = trigger && Math.max(0, _triggers.indexOf(self)) || 0,\n          i = triggerIndex,\n          cs,\n          bounds,\n          scroll,\n          isVertical,\n          override,\n          curTrigger,\n          curPin,\n          oppositeScroll,\n          initted,\n          revertedPins,\n          forcedOverflow,\n          markerStartOffset,\n          markerEndOffset;\n\n      if (markers && _isObject(position)) {\n        // if we alter the start/end positions with .setPositions(), it generally feeds in absolute NUMBERS which don't convey information about where to line up the markers, so to keep it intuitive, we record how far the trigger positions shift after applying the new numbers and then offset by that much in the opposite direction. We do the same to the associated trigger markers too of course.\n        markerStartOffset = gsap.getProperty(markerStartTrigger, direction.p);\n        markerEndOffset = gsap.getProperty(markerEndTrigger, direction.p);\n      }\n\n      while (i-- > 0) {\n        // user might try to pin the same element more than once, so we must find any prior triggers with the same pin, revert them, and determine how long they're pinning so that we can offset things appropriately. Make sure we revert from last to first so that things \"rewind\" properly.\n        curTrigger = _triggers[i];\n        curTrigger.end || curTrigger.refresh(0, 1) || (_refreshing = self); // if it's a timeline-based trigger that hasn't been fully initialized yet because it's waiting for 1 tick, just force the refresh() here, otherwise if it contains a pin that's supposed to affect other ScrollTriggers further down the page, they won't be adjusted properly.\n\n        curPin = curTrigger.pin;\n\n        if (curPin && (curPin === trigger || curPin === pin || curPin === pinnedContainer) && !curTrigger.isReverted) {\n          revertedPins || (revertedPins = []);\n          revertedPins.unshift(curTrigger); // we'll revert from first to last to make sure things reach their end state properly\n\n          curTrigger.revert(true, true);\n        }\n\n        if (curTrigger !== _triggers[i]) {\n          // in case it got removed.\n          triggerIndex--;\n          i--;\n        }\n      }\n\n      _isFunction(parsedStart) && (parsedStart = parsedStart(self));\n      parsedStart = _parseClamp(parsedStart, \"start\", self);\n      start = _parsePosition(parsedStart, trigger, size, direction, scrollFunc(), markerStart, markerStartTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._startClamp && \"_startClamp\") || (pin ? -0.001 : 0);\n      _isFunction(parsedEnd) && (parsedEnd = parsedEnd(self));\n\n      if (_isString(parsedEnd) && !parsedEnd.indexOf(\"+=\")) {\n        if (~parsedEnd.indexOf(\" \")) {\n          parsedEnd = (_isString(parsedStart) ? parsedStart.split(\" \")[0] : \"\") + parsedEnd;\n        } else {\n          offset = _offsetToPx(parsedEnd.substr(2), size);\n          parsedEnd = _isString(parsedStart) ? parsedStart : (containerAnimation ? gsap.utils.mapRange(0, containerAnimation.duration(), containerAnimation.scrollTrigger.start, containerAnimation.scrollTrigger.end, start) : start) + offset; // _parsePosition won't factor in the offset if the start is a number, so do it here.\n\n          parsedEndTrigger = trigger;\n        }\n      }\n\n      parsedEnd = _parseClamp(parsedEnd, \"end\", self);\n      end = Math.max(start, _parsePosition(parsedEnd || (parsedEndTrigger ? \"100% 0\" : max), parsedEndTrigger, size, direction, scrollFunc() + offset, markerEnd, markerEndTrigger, self, scrollerBounds, borderWidth, useFixedPosition, max, containerAnimation, self._endClamp && \"_endClamp\")) || -0.001;\n      offset = 0;\n      i = triggerIndex;\n\n      while (i--) {\n        curTrigger = _triggers[i];\n        curPin = curTrigger.pin;\n\n        if (curPin && curTrigger.start - curTrigger._pinPush <= start && !containerAnimation && curTrigger.end > 0) {\n          cs = curTrigger.end - (self._startClamp ? Math.max(0, curTrigger.start) : curTrigger.start);\n\n          if ((curPin === trigger && curTrigger.start - curTrigger._pinPush < start || curPin === pinnedContainer) && isNaN(parsedStart)) {\n            // numeric start values shouldn't be offset at all - treat them as absolute\n            offset += cs * (1 - curTrigger.progress);\n          }\n\n          curPin === pin && (otherPinOffset += cs);\n        }\n      }\n\n      start += offset;\n      end += offset;\n      self._startClamp && (self._startClamp += offset);\n\n      if (self._endClamp && !_refreshingAll) {\n        self._endClamp = end || -0.001;\n        end = Math.min(end, _maxScroll(scroller, direction));\n      }\n\n      change = end - start || (start -= 0.01) && 0.001;\n\n      if (isFirstRefresh) {\n        // on the very first refresh(), the prevProgress couldn't have been accurate yet because the start/end were never calculated, so we set it here. Before 3.11.5, it could lead to an inaccurate scroll position restoration with snapping.\n        prevProgress = gsap.utils.clamp(0, 1, gsap.utils.normalize(start, end, prevScroll));\n      }\n\n      self._pinPush = otherPinOffset;\n\n      if (markerStart && offset) {\n        // offset the markers if necessary\n        cs = {};\n        cs[direction.a] = \"+=\" + offset;\n        pinnedContainer && (cs[direction.p] = \"-=\" + scrollFunc());\n        gsap.set([markerStart, markerEnd], cs);\n      }\n\n      if (pin && !(_clampingMax && self.end >= _maxScroll(scroller, direction))) {\n        cs = _getComputedStyle(pin);\n        isVertical = direction === _vertical;\n        scroll = scrollFunc(); // recalculate because the triggers can affect the scroll\n\n        pinStart = parseFloat(pinGetter(direction.a)) + otherPinOffset;\n\n        if (!max && end > 1) {\n          // makes sure the scroller has a scrollbar, otherwise if something has width: 100%, for example, it would be too big (exclude the scrollbar). See https://gsap.com/forums/topic/25182-scrolltrigger-width-of-page-increase-where-markers-are-set-to-false/\n          forcedOverflow = (isViewport ? _doc.scrollingElement || _docEl : scroller).style;\n          forcedOverflow = {\n            style: forcedOverflow,\n            value: forcedOverflow[\"overflow\" + direction.a.toUpperCase()]\n          };\n\n          if (isViewport && _getComputedStyle(_body)[\"overflow\" + direction.a.toUpperCase()] !== \"scroll\") {\n            // avoid an extra scrollbar if BOTH <html> and <body> have overflow set to \"scroll\"\n            forcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = \"scroll\";\n          }\n        }\n\n        _swapPinIn(pin, spacer, cs);\n\n        pinState = _getState(pin); // transforms will interfere with the top/left/right/bottom placement, so remove them temporarily. getBoundingClientRect() factors in transforms.\n\n        bounds = _getBounds(pin, true);\n        oppositeScroll = useFixedPosition && _getScrollFunc(scroller, isVertical ? _horizontal : _vertical)();\n\n        if (pinSpacing) {\n          spacerState = [pinSpacing + direction.os2, change + otherPinOffset + _px];\n          spacerState.t = spacer;\n          i = pinSpacing === _padding ? _getSize(pin, direction) + change + otherPinOffset : 0;\n\n          if (i) {\n            spacerState.push(direction.d, i + _px); // for box-sizing: border-box (must include padding).\n\n            spacer.style.flexBasis !== \"auto\" && (spacer.style.flexBasis = i + _px);\n          }\n\n          _setState(spacerState);\n\n          if (pinnedContainer) {\n            // in ScrollTrigger.refresh(), we need to re-evaluate the pinContainer's size because this pinSpacing may stretch it out, but we can't just add the exact distance because depending on layout, it may not push things down or it may only do so partially.\n            _triggers.forEach(function (t) {\n              if (t.pin === pinnedContainer && t.vars.pinSpacing !== false) {\n                t._subPinOffset = true;\n              }\n            });\n          }\n\n          useFixedPosition && scrollFunc(prevScroll);\n        } else {\n          i = _getSize(pin, direction);\n          i && spacer.style.flexBasis !== \"auto\" && (spacer.style.flexBasis = i + _px);\n        }\n\n        if (useFixedPosition) {\n          override = {\n            top: bounds.top + (isVertical ? scroll - start : oppositeScroll) + _px,\n            left: bounds.left + (isVertical ? oppositeScroll : scroll - start) + _px,\n            boxSizing: \"border-box\",\n            position: \"fixed\"\n          };\n          override[_width] = override[\"max\" + _Width] = Math.ceil(bounds.width) + _px;\n          override[_height] = override[\"max\" + _Height] = Math.ceil(bounds.height) + _px;\n          override[_margin] = override[_margin + _Top] = override[_margin + _Right] = override[_margin + _Bottom] = override[_margin + _Left] = \"0\";\n          override[_padding] = cs[_padding];\n          override[_padding + _Top] = cs[_padding + _Top];\n          override[_padding + _Right] = cs[_padding + _Right];\n          override[_padding + _Bottom] = cs[_padding + _Bottom];\n          override[_padding + _Left] = cs[_padding + _Left];\n          pinActiveState = _copyState(pinOriginalState, override, pinReparent);\n          _refreshingAll && scrollFunc(0);\n        }\n\n        if (animation) {\n          // the animation might be affecting the transform, so we must jump to the end, check the value, and compensate accordingly. Otherwise, when it becomes unpinned, the pinSetter() will get set to a value that doesn't include whatever the animation did.\n          initted = animation._initted; // if not, we must invalidate() after this step, otherwise it could lock in starting values prematurely.\n\n          _suppressOverwrites(1);\n\n          animation.render(animation.duration(), true, true);\n          pinChange = pinGetter(direction.a) - pinStart + change + otherPinOffset;\n          pinMoves = Math.abs(change - pinChange) > 1;\n          useFixedPosition && pinMoves && pinActiveState.splice(pinActiveState.length - 2, 2); // transform is the last property/value set in the state Array. Since the animation is controlling that, we should omit it.\n\n          animation.render(0, true, true);\n          initted || animation.invalidate(true);\n          animation.parent || animation.totalTime(animation.totalTime()); // if, for example, a toggleAction called play() and then refresh() happens and when we render(1) above, it would cause the animation to complete and get removed from its parent, so this makes sure it gets put back in.\n\n          _suppressOverwrites(0);\n        } else {\n          pinChange = change;\n        }\n\n        forcedOverflow && (forcedOverflow.value ? forcedOverflow.style[\"overflow\" + direction.a.toUpperCase()] = forcedOverflow.value : forcedOverflow.style.removeProperty(\"overflow-\" + direction.a));\n      } else if (trigger && scrollFunc() && !containerAnimation) {\n        // it may be INSIDE a pinned element, so walk up the tree and look for any elements with _pinOffset to compensate because anything with pinSpacing that's already scrolled would throw off the measurements in getBoundingClientRect()\n        bounds = trigger.parentNode;\n\n        while (bounds && bounds !== _body) {\n          if (bounds._pinOffset) {\n            start -= bounds._pinOffset;\n            end -= bounds._pinOffset;\n          }\n\n          bounds = bounds.parentNode;\n        }\n      }\n\n      revertedPins && revertedPins.forEach(function (t) {\n        return t.revert(false, true);\n      });\n      self.start = start;\n      self.end = end;\n      scroll1 = scroll2 = _refreshingAll ? prevScroll : scrollFunc(); // reset velocity\n\n      if (!containerAnimation && !_refreshingAll) {\n        scroll1 < prevScroll && scrollFunc(prevScroll);\n        self.scroll.rec = 0;\n      }\n\n      self.revert(false, true);\n      lastRefresh = _getTime();\n\n      if (snapDelayedCall) {\n        lastSnap = -1; // just so snapping gets re-enabled, clear out any recorded last value\n        // self.isActive && scrollFunc(start + change * prevProgress); // previously this line was here to ensure that when snapping kicks in, it's from the previous progress but in some cases that's not desirable, like an all-page ScrollTrigger when new content gets added to the page, that'd totally change the progress.\n\n        snapDelayedCall.restart(true);\n      }\n\n      _refreshing = 0;\n      animation && isToggle && (animation._initted || prevAnimProgress) && animation.progress() !== prevAnimProgress && animation.progress(prevAnimProgress || 0, true).render(animation.time(), true, true); // must force a re-render because if saveStyles() was used on the target(s), the styles could have been wiped out during the refresh().\n\n      if (isFirstRefresh || prevProgress !== self.progress || containerAnimation || invalidateOnRefresh || animation && !animation._initted) {\n        // ensures that the direction is set properly (when refreshing, progress is set back to 0 initially, then back again to wherever it needs to be) and that callbacks are triggered.\n        animation && !isToggle && (animation._initted || prevProgress || animation.vars.immediateRender !== false) && animation.totalProgress(containerAnimation && start < -0.001 && !prevProgress ? gsap.utils.normalize(start, end, 0) : prevProgress, true); // to avoid issues where animation callbacks like onStart aren't triggered.\n\n        self.progress = isFirstRefresh || (scroll1 - start) / change === prevProgress ? 0 : prevProgress;\n      }\n\n      pin && pinSpacing && (spacer._pinOffset = Math.round(self.progress * pinChange));\n      scrubTween && scrubTween.invalidate();\n\n      if (!isNaN(markerStartOffset)) {\n        // numbers were passed in for the position which are absolute, so instead of just putting the markers at the very bottom of the viewport, we figure out how far they shifted down (it's safe to assume they were originally positioned in closer relation to the trigger element with values like \"top\", \"center\", a percentage or whatever, so we offset that much in the opposite direction to basically revert them to the relative position thy were at previously.\n        markerStartOffset -= gsap.getProperty(markerStartTrigger, direction.p);\n        markerEndOffset -= gsap.getProperty(markerEndTrigger, direction.p);\n\n        _shiftMarker(markerStartTrigger, direction, markerStartOffset);\n\n        _shiftMarker(markerStart, direction, markerStartOffset - (pinOffset || 0));\n\n        _shiftMarker(markerEndTrigger, direction, markerEndOffset);\n\n        _shiftMarker(markerEnd, direction, markerEndOffset - (pinOffset || 0));\n      }\n\n      isFirstRefresh && !_refreshingAll && self.update(); // edge case - when you reload a page when it's already scrolled down, some browsers fire a \"scroll\" event before DOMContentLoaded, triggering an updateAll(). If we don't update the self.progress as part of refresh(), then when it happens next, it may record prevProgress as 0 when it really shouldn't, potentially causing a callback in an animation to fire again.\n\n      if (onRefresh && !_refreshingAll && !executingOnRefresh) {\n        // when refreshing all, we do extra work to correct pinnedContainer sizes and ensure things don't exceed the maxScroll, so we should do all the refreshes at the end after all that work so that the start/end values are corrected.\n        executingOnRefresh = true;\n        onRefresh(self);\n        executingOnRefresh = false;\n      }\n    };\n\n    self.getVelocity = function () {\n      return (scrollFunc() - scroll2) / (_getTime() - _time2) * 1000 || 0;\n    };\n\n    self.endAnimation = function () {\n      _endAnimation(self.callbackAnimation);\n\n      if (animation) {\n        scrubTween ? scrubTween.progress(1) : !animation.paused() ? _endAnimation(animation, animation.reversed()) : isToggle || _endAnimation(animation, self.direction < 0, 1);\n      }\n    };\n\n    self.labelToScroll = function (label) {\n      return animation && animation.labels && (start || self.refresh() || start) + animation.labels[label] / animation.duration() * change || 0;\n    };\n\n    self.getTrailing = function (name) {\n      var i = _triggers.indexOf(self),\n          a = self.direction > 0 ? _triggers.slice(0, i).reverse() : _triggers.slice(i + 1);\n\n      return (_isString(name) ? a.filter(function (t) {\n        return t.vars.preventOverlaps === name;\n      }) : a).filter(function (t) {\n        return self.direction > 0 ? t.end <= start : t.start >= end;\n      });\n    };\n\n    self.update = function (reset, recordVelocity, forceFake) {\n      if (containerAnimation && !forceFake && !reset) {\n        return;\n      }\n\n      var scroll = _refreshingAll === true ? prevScroll : self.scroll(),\n          p = reset ? 0 : (scroll - start) / change,\n          clipped = p < 0 ? 0 : p > 1 ? 1 : p || 0,\n          prevProgress = self.progress,\n          isActive,\n          wasActive,\n          toggleState,\n          action,\n          stateChanged,\n          toggled,\n          isAtMax,\n          isTakingAction;\n\n      if (recordVelocity) {\n        scroll2 = scroll1;\n        scroll1 = containerAnimation ? scrollFunc() : scroll;\n\n        if (snap) {\n          snap2 = snap1;\n          snap1 = animation && !isToggle ? animation.totalProgress() : clipped;\n        }\n      } // anticipate the pinning a few ticks ahead of time based on velocity to avoid a visual glitch due to the fact that most browsers do scrolling on a separate thread (not synced with requestAnimationFrame).\n\n\n      if (anticipatePin && pin && !_refreshing && !_startup && _lastScrollTime) {\n        if (!clipped && start < scroll + (scroll - scroll2) / (_getTime() - _time2) * anticipatePin) {\n          clipped = 0.0001;\n        } else if (clipped === 1 && end > scroll + (scroll - scroll2) / (_getTime() - _time2) * anticipatePin) {\n          clipped = 0.9999;\n        }\n      }\n\n      if (clipped !== prevProgress && self.enabled) {\n        isActive = self.isActive = !!clipped && clipped < 1;\n        wasActive = !!prevProgress && prevProgress < 1;\n        toggled = isActive !== wasActive;\n        stateChanged = toggled || !!clipped !== !!prevProgress; // could go from start all the way to end, thus it didn't toggle but it did change state in a sense (may need to fire a callback)\n\n        self.direction = clipped > prevProgress ? 1 : -1;\n        self.progress = clipped;\n\n        if (stateChanged && !_refreshing) {\n          toggleState = clipped && !prevProgress ? 0 : clipped === 1 ? 1 : prevProgress === 1 ? 2 : 3; // 0 = enter, 1 = leave, 2 = enterBack, 3 = leaveBack (we prioritize the FIRST encounter, thus if you scroll really fast past the onEnter and onLeave in one tick, it'd prioritize onEnter.\n\n          if (isToggle) {\n            action = !toggled && toggleActions[toggleState + 1] !== \"none\" && toggleActions[toggleState + 1] || toggleActions[toggleState]; // if it didn't toggle, that means it shot right past and since we prioritize the \"enter\" action, we should switch to the \"leave\" in this case (but only if one is defined)\n\n            isTakingAction = animation && (action === \"complete\" || action === \"reset\" || action in animation);\n          }\n        }\n\n        preventOverlaps && (toggled || isTakingAction) && (isTakingAction || scrub || !animation) && (_isFunction(preventOverlaps) ? preventOverlaps(self) : self.getTrailing(preventOverlaps).forEach(function (t) {\n          return t.endAnimation();\n        }));\n\n        if (!isToggle) {\n          if (scrubTween && !_refreshing && !_startup) {\n            scrubTween._dp._time - scrubTween._start !== scrubTween._time && scrubTween.render(scrubTween._dp._time - scrubTween._start); // if there's a scrub on both the container animation and this one (or a ScrollSmoother), the update order would cause this one not to have rendered yet, so it wouldn't make any progress before we .restart() it heading toward the new progress so it'd appear stuck thus we force a render here.\n\n            if (scrubTween.resetTo) {\n              scrubTween.resetTo(\"totalProgress\", clipped, animation._tTime / animation._tDur);\n            } else {\n              // legacy support (courtesy), before 3.10.0\n              scrubTween.vars.totalProgress = clipped;\n              scrubTween.invalidate().restart();\n            }\n          } else if (animation) {\n            animation.totalProgress(clipped, !!(_refreshing && (lastRefresh || reset)));\n          }\n        }\n\n        if (pin) {\n          reset && pinSpacing && (spacer.style[pinSpacing + direction.os2] = spacingStart);\n\n          if (!useFixedPosition) {\n            pinSetter(_round(pinStart + pinChange * clipped));\n          } else if (stateChanged) {\n            isAtMax = !reset && clipped > prevProgress && end + 1 > scroll && scroll + 1 >= _maxScroll(scroller, direction); // if it's at the VERY end of the page, don't switch away from position: fixed because it's pointless and it could cause a brief flash when the user scrolls back up (when it gets pinned again)\n\n            if (pinReparent) {\n              if (!reset && (isActive || isAtMax)) {\n                var bounds = _getBounds(pin, true),\n                    _offset = scroll - start;\n\n                _reparent(pin, _body, bounds.top + (direction === _vertical ? _offset : 0) + _px, bounds.left + (direction === _vertical ? 0 : _offset) + _px);\n              } else {\n                _reparent(pin, spacer);\n              }\n            }\n\n            _setState(isActive || isAtMax ? pinActiveState : pinState);\n\n            pinMoves && clipped < 1 && isActive || pinSetter(pinStart + (clipped === 1 && !isAtMax ? pinChange : 0));\n          }\n        }\n\n        snap && !tweenTo.tween && !_refreshing && !_startup && snapDelayedCall.restart(true);\n        toggleClass && (toggled || once && clipped && (clipped < 1 || !_limitCallbacks)) && _toArray(toggleClass.targets).forEach(function (el) {\n          return el.classList[isActive || once ? \"add\" : \"remove\"](toggleClass.className);\n        }); // classes could affect positioning, so do it even if reset or refreshing is true.\n\n        onUpdate && !isToggle && !reset && onUpdate(self);\n\n        if (stateChanged && !_refreshing) {\n          if (isToggle) {\n            if (isTakingAction) {\n              if (action === \"complete\") {\n                animation.pause().totalProgress(1);\n              } else if (action === \"reset\") {\n                animation.restart(true).pause();\n              } else if (action === \"restart\") {\n                animation.restart(true);\n              } else {\n                animation[action]();\n              }\n            }\n\n            onUpdate && onUpdate(self);\n          }\n\n          if (toggled || !_limitCallbacks) {\n            // on startup, the page could be scrolled and we don't want to fire callbacks that didn't toggle. For example onEnter shouldn't fire if the ScrollTrigger isn't actually entered.\n            onToggle && toggled && _callback(self, onToggle);\n            callbacks[toggleState] && _callback(self, callbacks[toggleState]);\n            once && (clipped === 1 ? self.kill(false, 1) : callbacks[toggleState] = 0); // a callback shouldn't be called again if once is true.\n\n            if (!toggled) {\n              // it's possible to go completely past, like from before the start to after the end (or vice-versa) in which case BOTH callbacks should be fired in that order\n              toggleState = clipped === 1 ? 1 : 3;\n              callbacks[toggleState] && _callback(self, callbacks[toggleState]);\n            }\n          }\n\n          if (fastScrollEnd && !isActive && Math.abs(self.getVelocity()) > (_isNumber(fastScrollEnd) ? fastScrollEnd : 2500)) {\n            _endAnimation(self.callbackAnimation);\n\n            scrubTween ? scrubTween.progress(1) : _endAnimation(animation, action === \"reverse\" ? 1 : !clipped, 1);\n          }\n        } else if (isToggle && onUpdate && !_refreshing) {\n          onUpdate(self);\n        }\n      } // update absolutely-positioned markers (only if the scroller isn't the viewport)\n\n\n      if (markerEndSetter) {\n        var n = containerAnimation ? scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0) : scroll;\n        markerStartSetter(n + (markerStartTrigger._isFlipped ? 1 : 0));\n        markerEndSetter(n);\n      }\n\n      caMarkerSetter && caMarkerSetter(-scroll / containerAnimation.duration() * (containerAnimation._caScrollDist || 0));\n    };\n\n    self.enable = function (reset, refresh) {\n      if (!self.enabled) {\n        self.enabled = true;\n\n        _addListener(scroller, \"resize\", _onResize);\n\n        isViewport || _addListener(scroller, \"scroll\", _onScroll);\n        onRefreshInit && _addListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\n        if (reset !== false) {\n          self.progress = prevProgress = 0;\n          scroll1 = scroll2 = lastSnap = scrollFunc();\n        }\n\n        refresh !== false && self.refresh();\n      }\n    };\n\n    self.getTween = function (snap) {\n      return snap && tweenTo ? tweenTo.tween : scrubTween;\n    };\n\n    self.setPositions = function (newStart, newEnd, keepClamp, pinOffset) {\n      // doesn't persist after refresh()! Intended to be a way to override values that were set during refresh(), like you could set it in onRefresh()\n      if (containerAnimation) {\n        // convert ratios into scroll positions. Remember, start/end values on ScrollTriggers that have a containerAnimation refer to the time (in seconds), NOT scroll positions.\n        var st = containerAnimation.scrollTrigger,\n            duration = containerAnimation.duration(),\n            _change = st.end - st.start;\n\n        newStart = st.start + _change * newStart / duration;\n        newEnd = st.start + _change * newEnd / duration;\n      }\n\n      self.refresh(false, false, {\n        start: _keepClamp(newStart, keepClamp && !!self._startClamp),\n        end: _keepClamp(newEnd, keepClamp && !!self._endClamp)\n      }, pinOffset);\n      self.update();\n    };\n\n    self.adjustPinSpacing = function (amount) {\n      if (spacerState && amount) {\n        var i = spacerState.indexOf(direction.d) + 1;\n        spacerState[i] = parseFloat(spacerState[i]) + amount + _px;\n        spacerState[1] = parseFloat(spacerState[1]) + amount + _px;\n\n        _setState(spacerState);\n      }\n    };\n\n    self.disable = function (reset, allowAnimation) {\n      if (self.enabled) {\n        reset !== false && self.revert(true, true);\n        self.enabled = self.isActive = false;\n        allowAnimation || scrubTween && scrubTween.pause();\n        prevScroll = 0;\n        pinCache && (pinCache.uncache = 1);\n        onRefreshInit && _removeListener(ScrollTrigger, \"refreshInit\", onRefreshInit);\n\n        if (snapDelayedCall) {\n          snapDelayedCall.pause();\n          tweenTo.tween && tweenTo.tween.kill() && (tweenTo.tween = 0);\n        }\n\n        if (!isViewport) {\n          var i = _triggers.length;\n\n          while (i--) {\n            if (_triggers[i].scroller === scroller && _triggers[i] !== self) {\n              return; //don't remove the listeners if there are still other triggers referencing it.\n            }\n          }\n\n          _removeListener(scroller, \"resize\", _onResize);\n\n          isViewport || _removeListener(scroller, \"scroll\", _onScroll);\n        }\n      }\n    };\n\n    self.kill = function (revert, allowAnimation) {\n      self.disable(revert, allowAnimation);\n      scrubTween && !allowAnimation && scrubTween.kill();\n      id && delete _ids[id];\n\n      var i = _triggers.indexOf(self);\n\n      i >= 0 && _triggers.splice(i, 1);\n      i === _i && _direction > 0 && _i--; // if we're in the middle of a refresh() or update(), splicing would cause skips in the index, so adjust...\n      // if no other ScrollTrigger instances of the same scroller are found, wipe out any recorded scroll position. Otherwise, in a single page application, for example, it could maintain scroll position when it really shouldn't.\n\n      i = 0;\n\n      _triggers.forEach(function (t) {\n        return t.scroller === self.scroller && (i = 1);\n      });\n\n      i || _refreshingAll || (self.scroll.rec = 0);\n\n      if (animation) {\n        animation.scrollTrigger = null;\n        revert && animation.revert({\n          kill: false\n        });\n        allowAnimation || animation.kill();\n      }\n\n      markerStart && [markerStart, markerEnd, markerStartTrigger, markerEndTrigger].forEach(function (m) {\n        return m.parentNode && m.parentNode.removeChild(m);\n      });\n      _primary === self && (_primary = 0);\n\n      if (pin) {\n        pinCache && (pinCache.uncache = 1);\n        i = 0;\n\n        _triggers.forEach(function (t) {\n          return t.pin === pin && i++;\n        });\n\n        i || (pinCache.spacer = 0); // if there aren't any more ScrollTriggers with the same pin, remove the spacer, otherwise it could be contaminated with old/stale values if the user re-creates a ScrollTrigger for the same element.\n      }\n\n      vars.onKill && vars.onKill(self);\n    };\n\n    _triggers.push(self);\n\n    self.enable(false, false);\n    customRevertReturn && customRevertReturn(self);\n\n    if (animation && animation.add && !change) {\n      // if the animation is a timeline, it may not have been populated yet, so it wouldn't render at the proper place on the first refresh(), thus we should schedule one for the next tick. If \"change\" is defined, we know it must be re-enabling, thus we can refresh() right away.\n      var updateFunc = self.update; // some browsers may fire a scroll event BEFORE a tick elapses and/or the DOMContentLoaded fires. So there's a chance update() will be called BEFORE a refresh() has happened on a Timeline-attached ScrollTrigger which means the start/end won't be calculated yet. We don't want to add conditional logic inside the update() method (like check to see if end is defined and if not, force a refresh()) because that's a function that gets hit a LOT (performance). So we swap out the real update() method for this one that'll re-attach it the first time it gets called and of course forces a refresh().\n\n      self.update = function () {\n        self.update = updateFunc;\n        _scrollers.cache++; // otherwise a cached scroll position may get used in the refresh() in a very rare scenario, like if ScrollTriggers are created inside a DOMContentLoaded event and the queued requestAnimationFrame() fires beforehand. See https://gsap.com/community/forums/topic/41267-scrolltrigger-breaks-on-refresh-when-using-domcontentloaded/\n\n        start || end || self.refresh();\n      };\n\n      gsap.delayedCall(0.01, self.update);\n      change = 0.01;\n      start = end = 0;\n    } else {\n      self.refresh();\n    }\n\n    pin && _queueRefreshAll(); // pinning could affect the positions of other things, so make sure we queue a full refresh()\n  };\n\n  ScrollTrigger.register = function register(core) {\n    if (!_coreInitted) {\n      gsap = core || _getGSAP();\n      _windowExists() && window.document && ScrollTrigger.enable();\n      _coreInitted = _enabled;\n    }\n\n    return _coreInitted;\n  };\n\n  ScrollTrigger.defaults = function defaults(config) {\n    if (config) {\n      for (var p in config) {\n        _defaults[p] = config[p];\n      }\n    }\n\n    return _defaults;\n  };\n\n  ScrollTrigger.disable = function disable(reset, kill) {\n    _enabled = 0;\n\n    _triggers.forEach(function (trigger) {\n      return trigger[kill ? \"kill\" : \"disable\"](reset);\n    });\n\n    _removeListener(_win, \"wheel\", _onScroll);\n\n    _removeListener(_doc, \"scroll\", _onScroll);\n\n    clearInterval(_syncInterval);\n\n    _removeListener(_doc, \"touchcancel\", _passThrough);\n\n    _removeListener(_body, \"touchstart\", _passThrough);\n\n    _multiListener(_removeListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\n    _multiListener(_removeListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\n    _resizeDelay.kill();\n\n    _iterateAutoRefresh(_removeListener);\n\n    for (var i = 0; i < _scrollers.length; i += 3) {\n      _wheelListener(_removeListener, _scrollers[i], _scrollers[i + 1]);\n\n      _wheelListener(_removeListener, _scrollers[i], _scrollers[i + 2]);\n    }\n  };\n\n  ScrollTrigger.enable = function enable() {\n    _win = window;\n    _doc = document;\n    _docEl = _doc.documentElement;\n    _body = _doc.body;\n\n    if (gsap) {\n      _toArray = gsap.utils.toArray;\n      _clamp = gsap.utils.clamp;\n      _context = gsap.core.context || _passThrough;\n      _suppressOverwrites = gsap.core.suppressOverwrites || _passThrough;\n      _scrollRestoration = _win.history.scrollRestoration || \"auto\";\n      _lastScroll = _win.pageYOffset || 0;\n      gsap.core.globals(\"ScrollTrigger\", ScrollTrigger); // must register the global manually because in Internet Explorer, functions (classes) don't have a \"name\" property.\n\n      if (_body) {\n        _enabled = 1;\n        _div100vh = document.createElement(\"div\"); // to solve mobile browser address bar show/hide resizing, we shouldn't rely on window.innerHeight. Instead, use a <div> with its height set to 100vh and measure that since that's what the scrolling is based on anyway and it's not affected by address bar showing/hiding.\n\n        _div100vh.style.height = \"100vh\";\n        _div100vh.style.position = \"absolute\";\n\n        _refresh100vh();\n\n        _rafBugFix();\n\n        Observer.register(gsap); // isTouch is 0 if no touch, 1 if ONLY touch, and 2 if it can accommodate touch but also other types like mouse/pointer.\n\n        ScrollTrigger.isTouch = Observer.isTouch;\n        _fixIOSBug = Observer.isTouch && /(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent); // since 2017, iOS has had a bug that causes event.clientX/Y to be inaccurate when a scroll occurs, thus we must alternate ignoring every other touchmove event to work around it. See https://bugs.webkit.org/show_bug.cgi?id=181954 and https://codepen.io/GreenSock/pen/ExbrPNa/087cef197dc35445a0951e8935c41503\n\n        _ignoreMobileResize = Observer.isTouch === 1;\n\n        _addListener(_win, \"wheel\", _onScroll); // mostly for 3rd party smooth scrolling libraries.\n\n\n        _root = [_win, _doc, _docEl, _body];\n\n        if (gsap.matchMedia) {\n          ScrollTrigger.matchMedia = function (vars) {\n            var mm = gsap.matchMedia(),\n                p;\n\n            for (p in vars) {\n              mm.add(p, vars[p]);\n            }\n\n            return mm;\n          };\n\n          gsap.addEventListener(\"matchMediaInit\", function () {\n            return _revertAll();\n          });\n          gsap.addEventListener(\"matchMediaRevert\", function () {\n            return _revertRecorded();\n          });\n          gsap.addEventListener(\"matchMedia\", function () {\n            _refreshAll(0, 1);\n\n            _dispatch(\"matchMedia\");\n          });\n          gsap.matchMedia().add(\"(orientation: portrait)\", function () {\n            // when orientation changes, we should take new base measurements for the ignoreMobileResize feature.\n            _setBaseDimensions();\n\n            return _setBaseDimensions;\n          });\n        } else {\n          console.warn(\"Requires GSAP 3.11.0 or later\");\n        }\n\n        _setBaseDimensions();\n\n        _addListener(_doc, \"scroll\", _onScroll); // some browsers (like Chrome), the window stops dispatching scroll events on the window if you scroll really fast, but it's consistent on the document!\n\n\n        var bodyHasStyle = _body.hasAttribute(\"style\"),\n            bodyStyle = _body.style,\n            border = bodyStyle.borderTopStyle,\n            AnimationProto = gsap.core.Animation.prototype,\n            bounds,\n            i;\n\n        AnimationProto.revert || Object.defineProperty(AnimationProto, \"revert\", {\n          value: function value() {\n            return this.time(-0.01, true);\n          }\n        }); // only for backwards compatibility (Animation.revert() was added after 3.10.4)\n\n        bodyStyle.borderTopStyle = \"solid\"; // works around an issue where a margin of a child element could throw off the bounds of the _body, making it seem like there's a margin when there actually isn't. The border ensures that the bounds are accurate.\n\n        bounds = _getBounds(_body);\n        _vertical.m = Math.round(bounds.top + _vertical.sc()) || 0; // accommodate the offset of the <body> caused by margins and/or padding\n\n        _horizontal.m = Math.round(bounds.left + _horizontal.sc()) || 0;\n        border ? bodyStyle.borderTopStyle = border : bodyStyle.removeProperty(\"border-top-style\");\n\n        if (!bodyHasStyle) {\n          // SSR frameworks like Next.js complain if this attribute gets added.\n          _body.setAttribute(\"style\", \"\"); // it's not enough to just removeAttribute() - we must first set it to empty, otherwise Next.js complains.\n\n\n          _body.removeAttribute(\"style\");\n        } // TODO: (?) maybe move to leveraging the velocity mechanism in Observer and skip intervals.\n\n\n        _syncInterval = setInterval(_sync, 250);\n        gsap.delayedCall(0.5, function () {\n          return _startup = 0;\n        });\n\n        _addListener(_doc, \"touchcancel\", _passThrough); // some older Android devices intermittently stop dispatching \"touchmove\" events if we don't listen for \"touchcancel\" on the document.\n\n\n        _addListener(_body, \"touchstart\", _passThrough); //works around Safari bug: https://gsap.com/forums/topic/21450-draggable-in-iframe-on-mobile-is-buggy/\n\n\n        _multiListener(_addListener, _doc, \"pointerdown,touchstart,mousedown\", _pointerDownHandler);\n\n        _multiListener(_addListener, _doc, \"pointerup,touchend,mouseup\", _pointerUpHandler);\n\n        _transformProp = gsap.utils.checkPrefix(\"transform\");\n\n        _stateProps.push(_transformProp);\n\n        _coreInitted = _getTime();\n        _resizeDelay = gsap.delayedCall(0.2, _refreshAll).pause();\n        _autoRefresh = [_doc, \"visibilitychange\", function () {\n          var w = _win.innerWidth,\n              h = _win.innerHeight;\n\n          if (_doc.hidden) {\n            _prevWidth = w;\n            _prevHeight = h;\n          } else if (_prevWidth !== w || _prevHeight !== h) {\n            _onResize();\n          }\n        }, _doc, \"DOMContentLoaded\", _refreshAll, _win, \"load\", _refreshAll, _win, \"resize\", _onResize];\n\n        _iterateAutoRefresh(_addListener);\n\n        _triggers.forEach(function (trigger) {\n          return trigger.enable(0, 1);\n        });\n\n        for (i = 0; i < _scrollers.length; i += 3) {\n          _wheelListener(_removeListener, _scrollers[i], _scrollers[i + 1]);\n\n          _wheelListener(_removeListener, _scrollers[i], _scrollers[i + 2]);\n        }\n      }\n    }\n  };\n\n  ScrollTrigger.config = function config(vars) {\n    \"limitCallbacks\" in vars && (_limitCallbacks = !!vars.limitCallbacks);\n    var ms = vars.syncInterval;\n    ms && clearInterval(_syncInterval) || (_syncInterval = ms) && setInterval(_sync, ms);\n    \"ignoreMobileResize\" in vars && (_ignoreMobileResize = ScrollTrigger.isTouch === 1 && vars.ignoreMobileResize);\n\n    if (\"autoRefreshEvents\" in vars) {\n      _iterateAutoRefresh(_removeListener) || _iterateAutoRefresh(_addListener, vars.autoRefreshEvents || \"none\");\n      _ignoreResize = (vars.autoRefreshEvents + \"\").indexOf(\"resize\") === -1;\n    }\n  };\n\n  ScrollTrigger.scrollerProxy = function scrollerProxy(target, vars) {\n    var t = _getTarget(target),\n        i = _scrollers.indexOf(t),\n        isViewport = _isViewport(t);\n\n    if (~i) {\n      _scrollers.splice(i, isViewport ? 6 : 2);\n    }\n\n    if (vars) {\n      isViewport ? _proxies.unshift(_win, vars, _body, vars, _docEl, vars) : _proxies.unshift(t, vars);\n    }\n  };\n\n  ScrollTrigger.clearMatchMedia = function clearMatchMedia(query) {\n    _triggers.forEach(function (t) {\n      return t._ctx && t._ctx.query === query && t._ctx.kill(true, true);\n    });\n  };\n\n  ScrollTrigger.isInViewport = function isInViewport(element, ratio, horizontal) {\n    var bounds = (_isString(element) ? _getTarget(element) : element).getBoundingClientRect(),\n        offset = bounds[horizontal ? _width : _height] * ratio || 0;\n    return horizontal ? bounds.right - offset > 0 && bounds.left + offset < _win.innerWidth : bounds.bottom - offset > 0 && bounds.top + offset < _win.innerHeight;\n  };\n\n  ScrollTrigger.positionInViewport = function positionInViewport(element, referencePoint, horizontal) {\n    _isString(element) && (element = _getTarget(element));\n    var bounds = element.getBoundingClientRect(),\n        size = bounds[horizontal ? _width : _height],\n        offset = referencePoint == null ? size / 2 : referencePoint in _keywords ? _keywords[referencePoint] * size : ~referencePoint.indexOf(\"%\") ? parseFloat(referencePoint) * size / 100 : parseFloat(referencePoint) || 0;\n    return horizontal ? (bounds.left + offset) / _win.innerWidth : (bounds.top + offset) / _win.innerHeight;\n  };\n\n  ScrollTrigger.killAll = function killAll(allowListeners) {\n    _triggers.slice(0).forEach(function (t) {\n      return t.vars.id !== \"ScrollSmoother\" && t.kill();\n    });\n\n    if (allowListeners !== true) {\n      var listeners = _listeners.killAll || [];\n      _listeners = {};\n      listeners.forEach(function (f) {\n        return f();\n      });\n    }\n  };\n\n  return ScrollTrigger;\n}();\nScrollTrigger.version = \"3.13.0\";\n\nScrollTrigger.saveStyles = function (targets) {\n  return targets ? _toArray(targets).forEach(function (target) {\n    // saved styles are recorded in a consecutive alternating Array, like [element, cssText, transform attribute, cache, matchMedia, ...]\n    if (target && target.style) {\n      var i = _savedStyles.indexOf(target);\n\n      i >= 0 && _savedStyles.splice(i, 5);\n\n      _savedStyles.push(target, target.style.cssText, target.getBBox && target.getAttribute(\"transform\"), gsap.core.getCache(target), _context());\n    }\n  }) : _savedStyles;\n};\n\nScrollTrigger.revert = function (soft, media) {\n  return _revertAll(!soft, media);\n};\n\nScrollTrigger.create = function (vars, animation) {\n  return new ScrollTrigger(vars, animation);\n};\n\nScrollTrigger.refresh = function (safe) {\n  return safe ? _onResize(true) : (_coreInitted || ScrollTrigger.register()) && _refreshAll(true);\n};\n\nScrollTrigger.update = function (force) {\n  return ++_scrollers.cache && _updateAll(force === true ? 2 : 0);\n};\n\nScrollTrigger.clearScrollMemory = _clearScrollMemory;\n\nScrollTrigger.maxScroll = function (element, horizontal) {\n  return _maxScroll(element, horizontal ? _horizontal : _vertical);\n};\n\nScrollTrigger.getScrollFunc = function (element, horizontal) {\n  return _getScrollFunc(_getTarget(element), horizontal ? _horizontal : _vertical);\n};\n\nScrollTrigger.getById = function (id) {\n  return _ids[id];\n};\n\nScrollTrigger.getAll = function () {\n  return _triggers.filter(function (t) {\n    return t.vars.id !== \"ScrollSmoother\";\n  });\n}; // it's common for people to ScrollTrigger.getAll(t => t.kill()) on page routes, for example, and we don't want it to ruin smooth scrolling by killing the main ScrollSmoother one.\n\n\nScrollTrigger.isScrolling = function () {\n  return !!_lastScrollTime;\n};\n\nScrollTrigger.snapDirectional = _snapDirectional;\n\nScrollTrigger.addEventListener = function (type, callback) {\n  var a = _listeners[type] || (_listeners[type] = []);\n  ~a.indexOf(callback) || a.push(callback);\n};\n\nScrollTrigger.removeEventListener = function (type, callback) {\n  var a = _listeners[type],\n      i = a && a.indexOf(callback);\n  i >= 0 && a.splice(i, 1);\n};\n\nScrollTrigger.batch = function (targets, vars) {\n  var result = [],\n      varsCopy = {},\n      interval = vars.interval || 0.016,\n      batchMax = vars.batchMax || 1e9,\n      proxyCallback = function proxyCallback(type, callback) {\n    var elements = [],\n        triggers = [],\n        delay = gsap.delayedCall(interval, function () {\n      callback(elements, triggers);\n      elements = [];\n      triggers = [];\n    }).pause();\n    return function (self) {\n      elements.length || delay.restart(true);\n      elements.push(self.trigger);\n      triggers.push(self);\n      batchMax <= elements.length && delay.progress(1);\n    };\n  },\n      p;\n\n  for (p in vars) {\n    varsCopy[p] = p.substr(0, 2) === \"on\" && _isFunction(vars[p]) && p !== \"onRefreshInit\" ? proxyCallback(p, vars[p]) : vars[p];\n  }\n\n  if (_isFunction(batchMax)) {\n    batchMax = batchMax();\n\n    _addListener(ScrollTrigger, \"refresh\", function () {\n      return batchMax = vars.batchMax();\n    });\n  }\n\n  _toArray(targets).forEach(function (target) {\n    var config = {};\n\n    for (p in varsCopy) {\n      config[p] = varsCopy[p];\n    }\n\n    config.trigger = target;\n    result.push(ScrollTrigger.create(config));\n  });\n\n  return result;\n}; // to reduce file size. clamps the scroll and also returns a duration multiplier so that if the scroll gets chopped shorter, the duration gets curtailed as well (otherwise if you're very close to the top of the page, for example, and swipe up really fast, it'll suddenly slow down and take a long time to reach the top).\n\n\nvar _clampScrollAndGetDurationMultiplier = function _clampScrollAndGetDurationMultiplier(scrollFunc, current, end, max) {\n  current > max ? scrollFunc(max) : current < 0 && scrollFunc(0);\n  return end > max ? (max - current) / (end - current) : end < 0 ? current / (current - end) : 1;\n},\n    _allowNativePanning = function _allowNativePanning(target, direction) {\n  if (direction === true) {\n    target.style.removeProperty(\"touch-action\");\n  } else {\n    target.style.touchAction = direction === true ? \"auto\" : direction ? \"pan-\" + direction + (Observer.isTouch ? \" pinch-zoom\" : \"\") : \"none\"; // note: Firefox doesn't support it pinch-zoom properly, at least in addition to a pan-x or pan-y.\n  }\n\n  target === _docEl && _allowNativePanning(_body, direction);\n},\n    _overflow = {\n  auto: 1,\n  scroll: 1\n},\n    _nestedScroll = function _nestedScroll(_ref5) {\n  var event = _ref5.event,\n      target = _ref5.target,\n      axis = _ref5.axis;\n\n  var node = (event.changedTouches ? event.changedTouches[0] : event).target,\n      cache = node._gsap || gsap.core.getCache(node),\n      time = _getTime(),\n      cs;\n\n  if (!cache._isScrollT || time - cache._isScrollT > 2000) {\n    // cache for 2 seconds to improve performance.\n    while (node && node !== _body && (node.scrollHeight <= node.clientHeight && node.scrollWidth <= node.clientWidth || !(_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]))) {\n      node = node.parentNode;\n    }\n\n    cache._isScroll = node && node !== target && !_isViewport(node) && (_overflow[(cs = _getComputedStyle(node)).overflowY] || _overflow[cs.overflowX]);\n    cache._isScrollT = time;\n  }\n\n  if (cache._isScroll || axis === \"x\") {\n    event.stopPropagation();\n    event._gsapAllow = true;\n  }\n},\n    // capture events on scrollable elements INSIDE the <body> and allow those by calling stopPropagation() when we find a scrollable ancestor\n_inputObserver = function _inputObserver(target, type, inputs, nested) {\n  return Observer.create({\n    target: target,\n    capture: true,\n    debounce: false,\n    lockAxis: true,\n    type: type,\n    onWheel: nested = nested && _nestedScroll,\n    onPress: nested,\n    onDrag: nested,\n    onScroll: nested,\n    onEnable: function onEnable() {\n      return inputs && _addListener(_doc, Observer.eventTypes[0], _captureInputs, false, true);\n    },\n    onDisable: function onDisable() {\n      return _removeListener(_doc, Observer.eventTypes[0], _captureInputs, true);\n    }\n  });\n},\n    _inputExp = /(input|label|select|textarea)/i,\n    _inputIsFocused,\n    _captureInputs = function _captureInputs(e) {\n  var isInput = _inputExp.test(e.target.tagName);\n\n  if (isInput || _inputIsFocused) {\n    e._gsapAllow = true;\n    _inputIsFocused = isInput;\n  }\n},\n    _getScrollNormalizer = function _getScrollNormalizer(vars) {\n  _isObject(vars) || (vars = {});\n  vars.preventDefault = vars.isNormalizer = vars.allowClicks = true;\n  vars.type || (vars.type = \"wheel,touch\");\n  vars.debounce = !!vars.debounce;\n  vars.id = vars.id || \"normalizer\";\n\n  var _vars2 = vars,\n      normalizeScrollX = _vars2.normalizeScrollX,\n      momentum = _vars2.momentum,\n      allowNestedScroll = _vars2.allowNestedScroll,\n      onRelease = _vars2.onRelease,\n      self,\n      maxY,\n      target = _getTarget(vars.target) || _docEl,\n      smoother = gsap.core.globals().ScrollSmoother,\n      smootherInstance = smoother && smoother.get(),\n      content = _fixIOSBug && (vars.content && _getTarget(vars.content) || smootherInstance && vars.content !== false && !smootherInstance.smooth() && smootherInstance.content()),\n      scrollFuncY = _getScrollFunc(target, _vertical),\n      scrollFuncX = _getScrollFunc(target, _horizontal),\n      scale = 1,\n      initialScale = (Observer.isTouch && _win.visualViewport ? _win.visualViewport.scale * _win.visualViewport.width : _win.outerWidth) / _win.innerWidth,\n      wheelRefresh = 0,\n      resolveMomentumDuration = _isFunction(momentum) ? function () {\n    return momentum(self);\n  } : function () {\n    return momentum || 2.8;\n  },\n      lastRefreshID,\n      skipTouchMove,\n      inputObserver = _inputObserver(target, vars.type, true, allowNestedScroll),\n      resumeTouchMove = function resumeTouchMove() {\n    return skipTouchMove = false;\n  },\n      scrollClampX = _passThrough,\n      scrollClampY = _passThrough,\n      updateClamps = function updateClamps() {\n    maxY = _maxScroll(target, _vertical);\n    scrollClampY = _clamp(_fixIOSBug ? 1 : 0, maxY);\n    normalizeScrollX && (scrollClampX = _clamp(0, _maxScroll(target, _horizontal)));\n    lastRefreshID = _refreshID;\n  },\n      removeContentOffset = function removeContentOffset() {\n    content._gsap.y = _round(parseFloat(content._gsap.y) + scrollFuncY.offset) + \"px\";\n    content.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + parseFloat(content._gsap.y) + \", 0, 1)\";\n    scrollFuncY.offset = scrollFuncY.cacheID = 0;\n  },\n      ignoreDrag = function ignoreDrag() {\n    if (skipTouchMove) {\n      requestAnimationFrame(resumeTouchMove);\n\n      var offset = _round(self.deltaY / 2),\n          scroll = scrollClampY(scrollFuncY.v - offset);\n\n      if (content && scroll !== scrollFuncY.v + scrollFuncY.offset) {\n        scrollFuncY.offset = scroll - scrollFuncY.v;\n\n        var y = _round((parseFloat(content && content._gsap.y) || 0) - scrollFuncY.offset);\n\n        content.style.transform = \"matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, \" + y + \", 0, 1)\";\n        content._gsap.y = y + \"px\";\n        scrollFuncY.cacheID = _scrollers.cache;\n\n        _updateAll();\n      }\n\n      return true;\n    }\n\n    scrollFuncY.offset && removeContentOffset();\n    skipTouchMove = true;\n  },\n      tween,\n      startScrollX,\n      startScrollY,\n      onStopDelayedCall,\n      onResize = function onResize() {\n    // if the window resizes, like on an iPhone which Apple FORCES the address bar to show/hide even if we event.preventDefault(), it may be scrolling too far now that the address bar is showing, so we must dynamically adjust the momentum tween.\n    updateClamps();\n\n    if (tween.isActive() && tween.vars.scrollY > maxY) {\n      scrollFuncY() > maxY ? tween.progress(1) && scrollFuncY(maxY) : tween.resetTo(\"scrollY\", maxY);\n    }\n  };\n\n  content && gsap.set(content, {\n    y: \"+=0\"\n  }); // to ensure there's a cache (element._gsap)\n\n  vars.ignoreCheck = function (e) {\n    return _fixIOSBug && e.type === \"touchmove\" && ignoreDrag(e) || scale > 1.05 && e.type !== \"touchstart\" || self.isGesturing || e.touches && e.touches.length > 1;\n  };\n\n  vars.onPress = function () {\n    skipTouchMove = false;\n    var prevScale = scale;\n    scale = _round((_win.visualViewport && _win.visualViewport.scale || 1) / initialScale);\n    tween.pause();\n    prevScale !== scale && _allowNativePanning(target, scale > 1.01 ? true : normalizeScrollX ? false : \"x\");\n    startScrollX = scrollFuncX();\n    startScrollY = scrollFuncY();\n    updateClamps();\n    lastRefreshID = _refreshID;\n  };\n\n  vars.onRelease = vars.onGestureStart = function (self, wasDragging) {\n    scrollFuncY.offset && removeContentOffset();\n\n    if (!wasDragging) {\n      onStopDelayedCall.restart(true);\n    } else {\n      _scrollers.cache++; // make sure we're pulling the non-cached value\n      // alternate algorithm: durX = Math.min(6, Math.abs(self.velocityX / 800)),\tdur = Math.max(durX, Math.min(6, Math.abs(self.velocityY / 800))); dur = dur * (0.4 + (1 - _power4In(dur / 6)) * 0.6)) * (momentumSpeed || 1)\n\n      var dur = resolveMomentumDuration(),\n          currentScroll,\n          endScroll;\n\n      if (normalizeScrollX) {\n        currentScroll = scrollFuncX();\n        endScroll = currentScroll + dur * 0.05 * -self.velocityX / 0.227; // the constant .227 is from power4(0.05). velocity is inverted because scrolling goes in the opposite direction.\n\n        dur *= _clampScrollAndGetDurationMultiplier(scrollFuncX, currentScroll, endScroll, _maxScroll(target, _horizontal));\n        tween.vars.scrollX = scrollClampX(endScroll);\n      }\n\n      currentScroll = scrollFuncY();\n      endScroll = currentScroll + dur * 0.05 * -self.velocityY / 0.227; // the constant .227 is from power4(0.05)\n\n      dur *= _clampScrollAndGetDurationMultiplier(scrollFuncY, currentScroll, endScroll, _maxScroll(target, _vertical));\n      tween.vars.scrollY = scrollClampY(endScroll);\n      tween.invalidate().duration(dur).play(0.01);\n\n      if (_fixIOSBug && tween.vars.scrollY >= maxY || currentScroll >= maxY - 1) {\n        // iOS bug: it'll show the address bar but NOT fire the window \"resize\" event until the animation is done but we must protect against overshoot so we leverage an onUpdate to do so.\n        gsap.to({}, {\n          onUpdate: onResize,\n          duration: dur\n        });\n      }\n    }\n\n    onRelease && onRelease(self);\n  };\n\n  vars.onWheel = function () {\n    tween._ts && tween.pause();\n\n    if (_getTime() - wheelRefresh > 1000) {\n      // after 1 second, refresh the clamps otherwise that'll only happen when ScrollTrigger.refresh() is called or for touch-scrolling.\n      lastRefreshID = 0;\n      wheelRefresh = _getTime();\n    }\n  };\n\n  vars.onChange = function (self, dx, dy, xArray, yArray) {\n    _refreshID !== lastRefreshID && updateClamps();\n    dx && normalizeScrollX && scrollFuncX(scrollClampX(xArray[2] === dx ? startScrollX + (self.startX - self.x) : scrollFuncX() + dx - xArray[1])); // for more precision, we track pointer/touch movement from the start, otherwise it'll drift.\n\n    if (dy) {\n      scrollFuncY.offset && removeContentOffset();\n      var isTouch = yArray[2] === dy,\n          y = isTouch ? startScrollY + self.startY - self.y : scrollFuncY() + dy - yArray[1],\n          yClamped = scrollClampY(y);\n      isTouch && y !== yClamped && (startScrollY += yClamped - y);\n      scrollFuncY(yClamped);\n    }\n\n    (dy || dx) && _updateAll();\n  };\n\n  vars.onEnable = function () {\n    _allowNativePanning(target, normalizeScrollX ? false : \"x\");\n\n    ScrollTrigger.addEventListener(\"refresh\", onResize);\n\n    _addListener(_win, \"resize\", onResize);\n\n    if (scrollFuncY.smooth) {\n      scrollFuncY.target.style.scrollBehavior = \"auto\";\n      scrollFuncY.smooth = scrollFuncX.smooth = false;\n    }\n\n    inputObserver.enable();\n  };\n\n  vars.onDisable = function () {\n    _allowNativePanning(target, true);\n\n    _removeListener(_win, \"resize\", onResize);\n\n    ScrollTrigger.removeEventListener(\"refresh\", onResize);\n    inputObserver.kill();\n  };\n\n  vars.lockAxis = vars.lockAxis !== false;\n  self = new Observer(vars);\n  self.iOS = _fixIOSBug; // used in the Observer getCachedScroll() function to work around an iOS bug that wreaks havoc with TouchEvent.clientY if we allow scroll to go all the way back to 0.\n\n  _fixIOSBug && !scrollFuncY() && scrollFuncY(1); // iOS bug causes event.clientY values to freak out (wildly inaccurate) if the scroll position is exactly 0.\n\n  _fixIOSBug && gsap.ticker.add(_passThrough); // prevent the ticker from sleeping\n\n  onStopDelayedCall = self._dc;\n  tween = gsap.to(self, {\n    ease: \"power4\",\n    paused: true,\n    inherit: false,\n    scrollX: normalizeScrollX ? \"+=0.1\" : \"+=0\",\n    scrollY: \"+=0.1\",\n    modifiers: {\n      scrollY: _interruptionTracker(scrollFuncY, scrollFuncY(), function () {\n        return tween.pause();\n      })\n    },\n    onUpdate: _updateAll,\n    onComplete: onStopDelayedCall.vars.onComplete\n  }); // we need the modifier to sense if the scroll position is altered outside of the momentum tween (like with a scrollTo tween) so we can pause() it to prevent conflicts.\n\n  return self;\n};\n\nScrollTrigger.sort = function (func) {\n  if (_isFunction(func)) {\n    return _triggers.sort(func);\n  }\n\n  var scroll = _win.pageYOffset || 0;\n  ScrollTrigger.getAll().forEach(function (t) {\n    return t._sortY = t.trigger ? scroll + t.trigger.getBoundingClientRect().top : t.start + _win.innerHeight;\n  });\n  return _triggers.sort(func || function (a, b) {\n    return (a.vars.refreshPriority || 0) * -1e6 + (a.vars.containerAnimation ? 1e6 : a._sortY) - ((b.vars.containerAnimation ? 1e6 : b._sortY) + (b.vars.refreshPriority || 0) * -1e6);\n  }); // anything with a containerAnimation should refresh last.\n};\n\nScrollTrigger.observe = function (vars) {\n  return new Observer(vars);\n};\n\nScrollTrigger.normalizeScroll = function (vars) {\n  if (typeof vars === \"undefined\") {\n    return _normalizer;\n  }\n\n  if (vars === true && _normalizer) {\n    return _normalizer.enable();\n  }\n\n  if (vars === false) {\n    _normalizer && _normalizer.kill();\n    _normalizer = vars;\n    return;\n  }\n\n  var normalizer = vars instanceof Observer ? vars : _getScrollNormalizer(vars);\n  _normalizer && _normalizer.target === normalizer.target && _normalizer.kill();\n  _isViewport(normalizer.target) && (_normalizer = normalizer);\n  return normalizer;\n};\n\nScrollTrigger.core = {\n  // smaller file size way to leverage in ScrollSmoother and Observer\n  _getVelocityProp: _getVelocityProp,\n  _inputObserver: _inputObserver,\n  _scrollers: _scrollers,\n  _proxies: _proxies,\n  bridge: {\n    // when normalizeScroll sets the scroll position (ss = setScroll)\n    ss: function ss() {\n      _lastScrollTime || _dispatch(\"scrollStart\");\n      _lastScrollTime = _getTime();\n    },\n    // a way to get the _refreshing value in Observer\n    ref: function ref() {\n      return _refreshing;\n    }\n  }\n};\n_getGSAP() && gsap.registerPlugin(ScrollTrigger);\nexport { ScrollTrigger as default };"], "mappings": ";AAAA,SAAS,kBAAkB,QAAQ,OAAO;AAAE,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAAE,QAAI,aAAa,MAAM,CAAC;AAAG,eAAW,aAAa,WAAW,cAAc;AAAO,eAAW,eAAe;AAAM,QAAI,WAAW,WAAY,YAAW,WAAW;AAAM,WAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,EAAG;AAAE;AAE5T,SAAS,aAAa,aAAa,YAAY,aAAa;AAAE,MAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AAAG,MAAI,YAAa,mBAAkB,aAAa,WAAW;AAAG,SAAO;AAAa;AAYtN,IAAI;AAAJ,IACI;AADJ,IAEI;AAFJ,IAGI;AAHJ,IAII;AAJJ,IAKI;AALJ,IAMI;AANJ,IAOI;AAPJ,IAQI;AARJ,IASI;AATJ,IAUI;AAVJ,IAWI;AAXJ,IAYI;AAZJ,IAaI;AAbJ,IAcI,WAAW,SAASA,YAAW;AACjC,SAAO,QAAQ,OAAO,WAAW,gBAAgB,OAAO,OAAO,SAAS,KAAK,kBAAkB;AACjG;AAhBA,IAiBI,WAAW;AAjBf,IAkBI,aAAa,CAAC;AAlBlB,IAmBI,aAAa,CAAC;AAnBlB,IAoBI,WAAW,CAAC;AApBhB,IAqBI,WAAW,KAAK;AArBpB,IAsBI,UAAU,SAASC,SAAQ,MAAM,OAAO;AAC1C,SAAO;AACT;AAxBA,IAyBI,aAAa,SAASC,cAAa;AACrC,MAAI,OAAO,cAAc,MACrB,OAAO,KAAK,UAAU,CAAC,GACvB,YAAY,KAAK,YACjB,UAAU,KAAK;AACnB,YAAU,KAAK,MAAM,WAAW,UAAU;AAC1C,UAAQ,KAAK,MAAM,SAAS,QAAQ;AACpC,eAAa;AACb,aAAW;AAEX,YAAU,SAASD,SAAQ,MAAM,OAAO;AACtC,WAAO,KAAK,IAAI,EAAE,KAAK;AAAA,EACzB;AACF;AAtCA,IAuCI,gBAAgB,SAASE,eAAc,SAAS,UAAU;AAC5D,SAAO,CAAC,SAAS,QAAQ,OAAO,KAAK,SAAS,SAAS,QAAQ,OAAO,IAAI,CAAC,EAAE,QAAQ;AACvF;AAzCA,IA0CI,cAAc,SAASC,aAAY,IAAI;AACzC,SAAO,CAAC,CAAC,CAAC,MAAM,QAAQ,EAAE;AAC5B;AA5CA,IA6CI,eAAe,SAASC,cAAa,SAAS,MAAM,MAAM,SAAS,SAAS;AAC9E,SAAO,QAAQ,iBAAiB,MAAM,MAAM;AAAA,IAC1C,SAAS,YAAY;AAAA,IACrB,SAAS,CAAC,CAAC;AAAA,EACb,CAAC;AACH;AAlDA,IAmDI,kBAAkB,SAASC,iBAAgB,SAAS,MAAM,MAAM,SAAS;AAC3E,SAAO,QAAQ,oBAAoB,MAAM,MAAM,CAAC,CAAC,OAAO;AAC1D;AArDA,IAsDI,cAAc;AAtDlB,IAuDI,aAAa;AAvDjB,IAwDI,YAAY,SAASC,aAAY;AACnC,SAAO,eAAe,YAAY,aAAa,WAAW;AAC5D;AA1DA,IA2DI,mBAAmB,SAASC,kBAAiB,GAAG,YAAY;AAC9D,MAAI,cAAc,SAASC,aAAY,OAAO;AAE5C,QAAI,SAAS,UAAU,GAAG;AACxB,mBAAa,KAAK,QAAQ,oBAAoB;AAE9C,UAAI,gBAAgB,eAAe,YAAY;AAC/C,cAAQA,aAAY,IAAI,KAAK,MAAM,KAAK,MAAM,eAAe,YAAY,MAAM,IAAI;AAEnF,QAAE,KAAK;AACP,MAAAA,aAAY,UAAU,WAAW;AACjC,uBAAiB,QAAQ,MAAM,KAAK;AAAA,IACtC,WAAW,cAAc,WAAW,UAAUA,aAAY,WAAW,QAAQ,KAAK,GAAG;AACnF,MAAAA,aAAY,UAAU,WAAW;AACjC,MAAAA,aAAY,IAAI,EAAE;AAAA,IACpB;AAEA,WAAOA,aAAY,IAAIA,aAAY;AAAA,EACrC;AAEA,cAAY,SAAS;AACrB,SAAO,KAAK;AACd;AAjFA,IAkFI,cAAc;AAAA,EAChB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,IAAI,iBAAiB,SAAU,OAAO;AACpC,WAAO,UAAU,SAAS,KAAK,SAAS,OAAO,UAAU,GAAG,CAAC,IAAI,KAAK,eAAe,KAAK,WAAW,KAAK,OAAO,WAAW,KAAK,MAAM,WAAW,KAAK;AAAA,EACzJ,CAAC;AACH;AA9FA,IA+FI,YAAY;AAAA,EACd,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI,iBAAiB,SAAU,OAAO;AACpC,WAAO,UAAU,SAAS,KAAK,SAAS,YAAY,GAAG,GAAG,KAAK,IAAI,KAAK,eAAe,KAAK,UAAU,KAAK,OAAO,UAAU,KAAK,MAAM,UAAU,KAAK;AAAA,EACxJ,CAAC;AACH;AA5GA,IA6GI,aAAa,SAASC,YAAW,GAAG,MAAM;AAC5C,UAAQ,QAAQ,KAAK,QAAQ,KAAK,KAAK,YAAY,KAAK,MAAM,SAAS,CAAC,EAAE,CAAC,MAAM,OAAO,MAAM,YAAY,KAAK,OAAO,EAAE,mBAAmB,QAAQ,QAAQ,KAAK,sBAAsB,CAAC,IAAI;AAC7L;AA/GA,IAgHI,YAAY,SAASC,WAAU,SAAS,MAAM;AAEhD,MAAI,IAAI,KAAK;AAEb,SAAO,KAAK;AACV,QAAI,KAAK,CAAC,MAAM,WAAW,KAAK,CAAC,EAAE,SAAS,OAAO,GAAG;AACpD,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AA3HA,IA4HI,iBAAiB,SAASC,gBAAe,SAAS,MAAM;AAC1D,MAAI,IAAI,KAAK,GACT,KAAK,KAAK;AAEd,cAAY,OAAO,MAAM,UAAU,KAAK,oBAAoB;AAE5D,MAAI,IAAI,WAAW,QAAQ,OAAO,GAC9B,SAAS,OAAO,UAAU,KAAK,IAAI;AAEvC,GAAC,CAAC,MAAM,IAAI,WAAW,KAAK,OAAO,IAAI;AACvC,aAAW,IAAI,MAAM,KAAK,aAAa,SAAS,UAAU,SAAS;AAEnE,MAAI,OAAO,WAAW,IAAI,MAAM,GAC5B,OAAO,SAAS,WAAW,IAAI,MAAM,IAAI,iBAAiB,cAAc,SAAS,CAAC,GAAG,IAAI,MAAM,YAAY,OAAO,IAAI,KAAK,iBAAiB,SAAU,OAAO;AAC/J,WAAO,UAAU,SAAS,QAAQ,CAAC,IAAI,QAAQ,QAAQ,CAAC;AAAA,EAC1D,CAAC;AACD,OAAK,SAAS;AACd,WAAS,KAAK,SAAS,KAAK,YAAY,SAAS,gBAAgB,MAAM;AAEvE,SAAO;AACT;AAhJA,IAiJI,mBAAmB,SAASC,kBAAiB,OAAO,gBAAgB,UAAU;AAChF,MAAI,KAAK,OACL,KAAK,OACL,KAAK,SAAS,GACd,KAAK,IACL,MAAM,kBAAkB,IACxB,iBAAiB,KAAK,IAAI,KAAK,MAAM,CAAC,GACtC,SAAS,SAASC,QAAOC,QAAO,OAAO;AACzC,QAAI,IAAI,SAAS;AAEjB,QAAI,SAAS,IAAI,KAAK,KAAK;AACzB,WAAK;AACL,WAAKA;AACL,WAAK;AACL,WAAK;AAAA,IACP,WAAW,UAAU;AACnB,YAAMA;AAAA,IACR,OAAO;AAEL,WAAK,MAAMA,SAAQ,OAAO,IAAI,OAAO,KAAK;AAAA,IAC5C;AAAA,EACF,GACI,QAAQ,SAASC,SAAQ;AAC3B,SAAK,KAAK,WAAW,IAAI;AACzB,SAAK,KAAK;AAAA,EACZ,GACI,cAAc,SAASC,aAAY,aAAa;AAClD,QAAI,OAAO,IACP,OAAO,IACP,IAAI,SAAS;AAEjB,KAAC,eAAe,gBAAgB,MAAM,gBAAgB,MAAM,OAAO,WAAW;AAC9E,WAAO,OAAO,MAAM,IAAI,KAAK,iBAAiB,KAAK,MAAM,WAAW,OAAO,CAAC,WAAW,WAAW,IAAI,MAAM,QAAQ;AAAA,EACtH;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAzLA,IA0LI,YAAY,SAASC,WAAU,GAAG,gBAAgB;AACpD,oBAAkB,CAAC,EAAE,cAAc,EAAE,eAAe;AACpD,SAAO,EAAE,iBAAiB,EAAE,eAAe,CAAC,IAAI;AAClD;AA7LA,IA8LI,kBAAkB,SAASC,iBAAgB,GAAG;AAChD,MAAI,MAAM,KAAK,IAAI,MAAM,MAAM,CAAC,GAC5B,MAAM,KAAK,IAAI,MAAM,MAAM,CAAC;AAChC,SAAO,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,IAAI,MAAM;AAChD;AAlMA,IAmMI,oBAAoB,SAASC,qBAAoB;AACnD,kBAAgB,KAAK,KAAK,QAAQ,EAAE;AACpC,mBAAiB,cAAc,QAAQ,WAAW;AACpD;AAtMA,IAuMI,YAAY,SAASC,WAAU,MAAM;AACvC,SAAO,QAAQ,SAAS;AAExB,MAAI,CAAC,gBAAgB,QAAQ,OAAO,aAAa,eAAe,SAAS,MAAM;AAC7E,WAAO;AACP,WAAO;AACP,aAAS,KAAK;AACd,YAAQ,KAAK;AACb,YAAQ,CAAC,MAAM,MAAM,QAAQ,KAAK;AAClC,aAAS,KAAK,MAAM;AAEpB,eAAW,KAAK,KAAK,WAAW,WAAY;AAAA,IAAC;AAE7C,mBAAe,oBAAoB,QAAQ,YAAY;AAEvD,eAAW,SAAS,UAAU,KAAK,cAAc,KAAK,WAAW,kCAAkC,EAAE,UAAU,IAAI,kBAAkB,QAAQ,UAAU,iBAAiB,KAAK,UAAU,mBAAmB,IAAI,IAAI;AAClN,kBAAc,SAAS,cAAc,kBAAkB,SAAS,8CAA8C,EAAE,mBAAmB,UAAU,wCAAwC,mDAAmD,MAAM,GAAG;AACjP,eAAW,WAAY;AACrB,aAAO,WAAW;AAAA,IACpB,GAAG,GAAG;AAEN,sBAAkB;AAElB,mBAAe;AAAA,EACjB;AAEA,SAAO;AACT;AAEA,YAAY,KAAK;AACjB,WAAW,QAAQ;AACZ,IAAI,WAAwB,WAAY;AAC7C,WAASC,UAAS,MAAM;AACtB,SAAK,KAAK,IAAI;AAAA,EAChB;AAEA,MAAI,SAASA,UAAS;AAEtB,SAAO,OAAO,SAAS,KAAK,MAAM;AAChC,oBAAgB,UAAU,IAAI,KAAK,QAAQ,KAAK,sCAAsC;AACtF,qBAAiB,kBAAkB;AACnC,QAAI,YAAY,KAAK,WACjB,cAAc,KAAK,aACnB,OAAO,KAAK,MACZ,SAAS,KAAK,QACd,aAAa,KAAK,YAClB,WAAW,KAAK,UAChB,iBAAiB,KAAK,gBACtB,SAAS,KAAK,QACd,cAAc,KAAK,aACnB,SAAS,KAAK,QACd,aAAa,KAAK,YAClB,QAAQ,KAAK,OACb,cAAc,KAAK,aACnB,YAAY,KAAK,WACjB,SAAS,KAAK,QACd,UAAU,KAAK,SACf,YAAY,KAAK,WACjB,UAAU,KAAK,SACf,SAAS,KAAK,QACd,OAAO,KAAK,MACZ,SAAS,KAAK,QACd,YAAY,KAAK,WACjB,YAAY,KAAK,WACjB,WAAW,KAAK,UAChB,YAAY,KAAK,WACjB,YAAY,KAAK,WACjB,UAAU,KAAK,SACf,aAAa,KAAK,YAClB,SAAS,KAAK,QACd,cAAc,KAAK,aACnB,eAAe,KAAK,cACpB,iBAAiB,KAAK,gBACtB,eAAe,KAAK,cACpB,UAAU,KAAK,SACf,WAAW,KAAK,UAChB,YAAY,KAAK,WACjB,UAAU,KAAK,SACf,cAAc,KAAK,aACnB,UAAU,KAAK,SACf,cAAc,KAAK,aACnB,WAAW,KAAK,UAChB,aAAa,KAAK;AACtB,SAAK,SAAS,SAAS,WAAW,MAAM,KAAK;AAC7C,SAAK,OAAO;AACZ,eAAW,SAAS,KAAK,MAAM,QAAQ,MAAM;AAC7C,gBAAY,aAAa;AACzB,kBAAc,eAAe;AAC7B,iBAAa,cAAc;AAC3B,kBAAc,eAAe;AAC7B,WAAO,QAAQ;AACf,eAAW,aAAa;AACxB,mBAAe,aAAa,WAAW,KAAK,iBAAiB,KAAK,EAAE,UAAU,KAAK;AAEnF,QAAI,IACA,mBACA,SACA,OACA,SACA,QACA,MACA,OAAO,MACP,aAAa,GACb,aAAa,GACb,UAAU,KAAK,WAAW,CAAC,kBAAkB,KAAK,YAAY,OAC9D,cAAc,eAAe,QAAQ,WAAW,GAChD,cAAc,eAAe,QAAQ,SAAS,GAC9C,UAAU,YAAY,GACtB,UAAU,YAAY,GACtB,eAAe,CAAC,KAAK,QAAQ,OAAO,KAAK,CAAC,CAAC,KAAK,QAAQ,SAAS,KAAK,YAAY,CAAC,MAAM,eAE7F,aAAa,YAAY,MAAM,GAC3B,WAAW,OAAO,iBAAiB,MACnC,SAAS,CAAC,GAAG,GAAG,CAAC,GAErB,SAAS,CAAC,GAAG,GAAG,CAAC,GACb,cAAc,GACd,eAAe,SAASC,gBAAe;AACzC,aAAO,cAAc,SAAS;AAAA,IAChC,GACI,eAAe,SAASC,cAAa,GAAG,kBAAkB;AAC5D,cAAQ,KAAK,QAAQ,MAAM,UAAU,UAAU,EAAE,QAAQ,MAAM,KAAK,oBAAoB,gBAAgB,EAAE,gBAAgB,WAAW,eAAe,YAAY,GAAG,gBAAgB;AAAA,IACrL,GACI,aAAa,SAASC,cAAa;AACrC,WAAK,IAAI,MAAM;AAEf,WAAK,IAAI,MAAM;AAEf,wBAAkB,MAAM;AACxB,gBAAU,OAAO,IAAI;AAAA,IACvB,GACI,SAAS,SAASX,UAAS;AAC7B,UAAI,KAAK,KAAK,SAAS,gBAAgB,MAAM,GACzC,KAAK,KAAK,SAAS,gBAAgB,MAAM,GACzC,WAAW,KAAK,IAAI,EAAE,KAAK,WAC3B,WAAW,KAAK,IAAI,EAAE,KAAK;AAE/B,mBAAa,YAAY,aAAa,SAAS,MAAM,IAAI,IAAI,QAAQ,MAAM;AAE3E,UAAI,UAAU;AACZ,mBAAW,KAAK,SAAS,KAAK,QAAQ,IAAI;AAC1C,kBAAU,KAAK,SAAS,KAAK,OAAO,IAAI;AACxC,qBAAa,UAAU,IAAI;AAC3B,qBAAa,KAAK,SAAS,MAAM,aAAa,KAAK,UAAU,IAAI;AACjE,qBAAa,KAAK;AAClB,eAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI;AAAA,MACtC;AAEA,UAAI,UAAU;AACZ,kBAAU,KAAK,SAAS,KAAK,OAAO,IAAI;AACxC,gBAAQ,KAAK,SAAS,KAAK,KAAK,IAAI;AACpC,qBAAa,UAAU,IAAI;AAC3B,qBAAa,KAAK,SAAS,MAAM,aAAa,KAAK,UAAU,IAAI;AACjE,qBAAa,KAAK;AAClB,eAAO,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,IAAI;AAAA,MACtC;AAEA,UAAI,SAAS,SAAS;AACpB,kBAAU,OAAO,IAAI;AAErB,YAAI,SAAS;AACX,yBAAe,YAAY,KAAK,YAAY,IAAI;AAChD,oBAAU,OAAO,IAAI;AACrB,oBAAU;AAAA,QACZ;AAEA,gBAAQ;AAAA,MACV;AAEA,gBAAU,EAAE,SAAS,UAAU,cAAc,WAAW,IAAI;AAE5D,UAAI,SAAS;AACX,gBAAQ,IAAI;AACZ,kBAAU;AAAA,MACZ;AAEA,WAAK;AAAA,IACP,GACI,UAAU,SAASY,SAAQ,GAAG,GAAG,OAAO;AAC1C,aAAO,KAAK,KAAK;AACjB,aAAO,KAAK,KAAK;AAEjB,WAAK,IAAI,OAAO,CAAC;AAEjB,WAAK,IAAI,OAAO,CAAC;AAEjB,iBAAW,OAAO,KAAK,sBAAsB,MAAM,KAAK,OAAO;AAAA,IACjE,GACI,wBAAwB,SAASC,uBAAsB,GAAG,GAAG;AAC/D,UAAI,YAAY,CAAC,MAAM;AACrB,aAAK,OAAO,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,MAAM;AACrD,iBAAS;AAAA,MACX;AAEA,UAAI,SAAS,KAAK;AAChB,eAAO,CAAC,KAAK;AAEb,aAAK,IAAI,OAAO,GAAG,IAAI;AAAA,MAEzB;AAEA,UAAI,SAAS,KAAK;AAChB,eAAO,CAAC,KAAK;AAEb,aAAK,IAAI,OAAO,GAAG,IAAI;AAAA,MACzB;AAEA,iBAAW,OAAO,KAAK,sBAAsB,MAAM,KAAK,OAAO;AAAA,IACjE,GACI,UAAU,SAASC,SAAQ,GAAG;AAChC,UAAI,aAAa,GAAG,CAAC,GAAG;AACtB;AAAA,MACF;AAEA,UAAI,UAAU,GAAG,cAAc;AAC/B,UAAI,IAAI,EAAE,SACN,IAAI,EAAE,SACN,KAAK,IAAI,KAAK,GACd,KAAK,IAAI,KAAK,GACd,aAAa,KAAK;AACtB,WAAK,IAAI;AACT,WAAK,IAAI;AAET,UAAI,eAAe,MAAM,QAAQ,KAAK,IAAI,KAAK,SAAS,CAAC,KAAK,eAAe,KAAK,IAAI,KAAK,SAAS,CAAC,KAAK,cAAc;AACtH,kBAAU,aAAa,IAAI;AAE3B,uBAAe,KAAK,aAAa;AACjC,8BAAsB,IAAI,EAAE;AAAA,MAC9B;AAAA,IACF,GACI,WAAW,KAAK,UAAU,SAAU,GAAG;AACzC,UAAI,aAAa,GAAG,CAAC,KAAK,KAAK,EAAE,QAAQ;AACvC;AAAA,MACF;AAEA,WAAK,OAAO,OAAO;AACnB,wBAAkB,MAAM;AACxB,WAAK,YAAY;AACjB,UAAI,UAAU,CAAC;AAEf,mBAAa,aAAa;AAC1B,WAAK,SAAS,KAAK,IAAI,EAAE;AACzB,WAAK,SAAS,KAAK,IAAI,EAAE;AAEzB,WAAK,IAAI,MAAM;AAGf,WAAK,IAAI,MAAM;AAEf,mBAAa,eAAe,SAAS,UAAU,YAAY,CAAC,GAAG,SAAS,SAAS,IAAI;AAErF,WAAK,SAAS,KAAK,SAAS;AAC5B,iBAAW,QAAQ,IAAI;AAAA,IACzB,GACI,aAAa,KAAK,YAAY,SAAU,GAAG;AAC7C,UAAI,aAAa,GAAG,CAAC,GAAG;AACtB;AAAA,MACF;AAEA,sBAAgB,eAAe,SAAS,UAAU,YAAY,CAAC,GAAG,SAAS,IAAI;AAE/E,UAAI,iBAAiB,CAAC,MAAM,KAAK,IAAI,KAAK,MAAM,GAC5C,cAAc,KAAK,YACnB,iBAAiB,gBAAgB,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,IAE5G,YAAY,UAAU,CAAC;AAEvB,UAAI,CAAC,kBAAkB,gBAAgB;AACrC,aAAK,IAAI,MAAM;AAEf,aAAK,IAAI,MAAM;AAGf,YAAI,kBAAkB,aAAa;AACjC,eAAK,YAAY,MAAM,WAAY;AAEjC,gBAAI,SAAS,IAAI,cAAc,OAAO,CAAC,EAAE,kBAAkB;AACzD,kBAAI,EAAE,OAAO,OAAO;AAElB,kBAAE,OAAO,MAAM;AAAA,cACjB,WAAW,SAAS,aAAa;AAC/B,oBAAI,iBAAiB,SAAS,YAAY,aAAa;AACvD,+BAAe,eAAe,SAAS,MAAM,MAAM,MAAM,GAAG,UAAU,SAAS,UAAU,SAAS,UAAU,SAAS,UAAU,SAAS,OAAO,OAAO,OAAO,OAAO,GAAG,IAAI;AAC3K,kBAAE,OAAO,cAAc,cAAc;AAAA,cACvC;AAAA,YACF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAEA,WAAK,aAAa,KAAK,cAAc,KAAK,YAAY;AACtD,gBAAU,eAAe,CAAC,gBAAgB,kBAAkB,QAAQ,IAAI;AACxE,iBAAW,OAAO;AAElB,mBAAa,eAAe,UAAU,IAAI;AAC1C,mBAAa,UAAU,MAAM,cAAc;AAAA,IAC7C,GACI,kBAAkB,SAASC,iBAAgB,GAAG;AAChD,aAAO,EAAE,WAAW,EAAE,QAAQ,SAAS,MAAM,KAAK,cAAc,SAAS,eAAe,GAAG,KAAK,UAAU;AAAA,IAC5G,GACI,gBAAgB,SAASC,iBAAgB;AAC3C,cAAQ,KAAK,cAAc,UAAU,aAAa,IAAI;AAAA,IACxD,GACI,WAAW,SAASC,UAAS,GAAG;AAClC,UAAI,aAAa,CAAC,GAAG;AACnB;AAAA,MACF;AAEA,UAAI,IAAI,YAAY,GAChB,IAAI,YAAY;AACpB,eAAS,IAAI,WAAW,cAAc,IAAI,WAAW,aAAa,CAAC;AACnE,gBAAU;AACV,gBAAU;AACV,gBAAU,kBAAkB,QAAQ,IAAI;AAAA,IAC1C,GACI,WAAW,SAASC,UAAS,GAAG;AAClC,UAAI,aAAa,CAAC,GAAG;AACnB;AAAA,MACF;AAEA,UAAI,UAAU,GAAG,cAAc;AAC/B,kBAAY,UAAU;AACtB,UAAI,cAAc,EAAE,cAAc,IAAI,aAAa,EAAE,cAAc,IAAI,KAAK,cAAc,KAAK;AAC/F,cAAQ,EAAE,SAAS,YAAY,EAAE,SAAS,YAAY,CAAC;AACvD,gBAAU,CAAC,gBAAgB,kBAAkB,QAAQ,IAAI;AAAA,IAC3D,GACI,UAAU,SAASC,SAAQ,GAAG;AAChC,UAAI,aAAa,CAAC,GAAG;AACnB;AAAA,MACF;AAEA,UAAI,IAAI,EAAE,SACN,IAAI,EAAE,SACN,KAAK,IAAI,KAAK,GACd,KAAK,IAAI,KAAK;AAClB,WAAK,IAAI;AACT,WAAK,IAAI;AACT,cAAQ;AACR,gBAAU,kBAAkB,QAAQ,IAAI;AACxC,OAAC,MAAM,OAAO,sBAAsB,IAAI,EAAE;AAAA,IAC5C,GACI,WAAW,SAASC,UAAS,GAAG;AAClC,WAAK,QAAQ;AACb,cAAQ,IAAI;AAAA,IACd,GACI,cAAc,SAASC,aAAY,GAAG;AACxC,WAAK,QAAQ;AACb,iBAAW,IAAI;AAAA,IACjB,GACI,WAAW,SAASC,UAAS,GAAG;AAClC,aAAO,aAAa,CAAC,KAAK,UAAU,GAAG,cAAc,KAAK,QAAQ,IAAI;AAAA,IACxE;AAEA,wBAAoB,KAAK,MAAM,KAAK,YAAY,eAAe,MAAM,UAAU,EAAE,MAAM;AACvF,SAAK,SAAS,KAAK,SAAS;AAC5B,SAAK,MAAM,iBAAiB,GAAG,IAAI,IAAI;AACvC,SAAK,MAAM,iBAAiB,GAAG,IAAI,IAAI;AACvC,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,aAAa,KAAK,cAAc,KAAK,YAAY;AAEtD,aAAS,IAAI;AAEb,SAAK,SAAS,SAAU,GAAG;AACzB,UAAI,CAAC,KAAK,WAAW;AACnB,qBAAa,aAAa,WAAW,QAAQ,UAAU,SAAS;AAEhE,aAAK,QAAQ,QAAQ,KAAK,KAAK,aAAa,aAAa,WAAW,QAAQ,UAAU,UAAU,SAAS,OAAO;AAChH,aAAK,QAAQ,OAAO,KAAK,KAAK,aAAa,QAAQ,SAAS,UAAU,SAAS,OAAO;AAEtF,YAAI,KAAK,QAAQ,OAAO,KAAK,KAAK,YAAY,KAAK,QAAQ,SAAS,KAAK,GAAG;AAC1E,uBAAa,QAAQ,YAAY,CAAC,GAAG,UAAU,SAAS,OAAO;AAE/D,uBAAa,UAAU,YAAY,CAAC,GAAG,UAAU;AAEjD,uBAAa,UAAU,YAAY,CAAC,GAAG,UAAU;AAEjD,yBAAe,aAAa,QAAQ,SAAS,cAAc,MAAM,IAAI;AACrE,qBAAW,aAAa,QAAQ,SAAS,QAAQ;AACjD,4BAAkB,aAAa,UAAU,gBAAgB,eAAe;AACxE,0BAAgB,aAAa,UAAU,cAAc,aAAa;AAClE,qBAAW,aAAa,QAAQ,eAAe,SAAS,QAAQ;AAChE,wBAAc,aAAa,QAAQ,eAAe,SAAS,WAAW;AACtE,oBAAU,aAAa,QAAQ,eAAe,QAAQ,OAAO;AAAA,QAC/D;AAEA,aAAK,YAAY;AACjB,aAAK,aAAa,KAAK,cAAc,KAAK,YAAY,QAAQ,UAAU;AAExE,aAAK,IAAI,MAAM;AAEf,aAAK,IAAI,MAAM;AAEf,kBAAU,YAAY;AACtB,kBAAU,YAAY;AACtB,aAAK,EAAE,QAAQ,SAAS,CAAC;AACzB,oBAAY,SAAS,IAAI;AAAA,MAC3B;AAEA,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,WAAY;AACzB,UAAI,KAAK,WAAW;AAElB,mBAAW,OAAO,SAAU,GAAG;AAC7B,iBAAO,MAAM,QAAQ,YAAY,EAAE,MAAM;AAAA,QAC3C,CAAC,EAAE,UAAU,gBAAgB,aAAa,WAAW,QAAQ,UAAU,SAAS;AAEhF,YAAI,KAAK,WAAW;AAClB,eAAK,IAAI,MAAM;AAEf,eAAK,IAAI,MAAM;AAEf,0BAAgB,eAAe,SAAS,UAAU,YAAY,CAAC,GAAG,SAAS,IAAI;AAAA,QACjF;AAEA,wBAAgB,aAAa,WAAW,QAAQ,UAAU,UAAU,OAAO;AAE3E,wBAAgB,QAAQ,SAAS,UAAU,OAAO;AAElD,wBAAgB,QAAQ,YAAY,CAAC,GAAG,UAAU,OAAO;AAEzD,wBAAgB,UAAU,YAAY,CAAC,GAAG,UAAU;AAEpD,wBAAgB,UAAU,YAAY,CAAC,GAAG,UAAU;AAEpD,wBAAgB,QAAQ,SAAS,cAAc,IAAI;AAEnD,wBAAgB,QAAQ,SAAS,QAAQ;AAEzC,wBAAgB,UAAU,gBAAgB,eAAe;AAEzD,wBAAgB,UAAU,cAAc,aAAa;AAErD,wBAAgB,QAAQ,eAAe,SAAS,QAAQ;AAExD,wBAAgB,QAAQ,eAAe,SAAS,WAAW;AAE3D,wBAAgB,QAAQ,eAAe,QAAQ,OAAO;AAEtD,aAAK,YAAY,KAAK,YAAY,KAAK,aAAa;AACpD,qBAAa,UAAU,IAAI;AAAA,MAC7B;AAAA,IACF;AAEA,SAAK,OAAO,KAAK,SAAS,WAAY;AACpC,WAAK,QAAQ;AAEb,UAAI,IAAI,WAAW,QAAQ,IAAI;AAE/B,WAAK,KAAK,WAAW,OAAO,GAAG,CAAC;AAChC,sBAAgB,SAAS,cAAc;AAAA,IACzC;AAEA,eAAW,KAAK,IAAI;AAEpB,oBAAgB,YAAY,MAAM,MAAM,cAAc;AACtD,SAAK,OAAO,KAAK;AAAA,EACnB;AAEA,eAAad,WAAU,CAAC;AAAA,IACtB,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,IAAI,YAAY;AAAA,IAC9B;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,KAAK,SAAS,MAAM;AAClB,aAAO,KAAK,IAAI,YAAY;AAAA,IAC9B;AAAA,EACF,CAAC,CAAC;AAEF,SAAOA;AACT,EAAE;AACF,SAAS,UAAU;AAEnB,SAAS,SAAS,SAAU,MAAM;AAChC,SAAO,IAAI,SAAS,IAAI;AAC1B;AAEA,SAAS,WAAW;AAEpB,SAAS,SAAS,WAAY;AAC5B,SAAO,WAAW,MAAM;AAC1B;AAEA,SAAS,UAAU,SAAU,IAAI;AAC/B,SAAO,WAAW,OAAO,SAAU,GAAG;AACpC,WAAO,EAAE,KAAK,OAAO;AAAA,EACvB,CAAC,EAAE,CAAC;AACN;AAEA,SAAS,KAAK,KAAK,eAAe,QAAQ;;;ACtrB1C,IAAIe;AAAJ,IACIC;AADJ,IAEIC;AAFJ,IAGIC;AAHJ,IAIIC;AAJJ,IAKIC;AALJ,IAMIC;AANJ,IAOI;AAPJ,IAQI;AARJ,IASIC;AATJ,IAUI;AAVJ,IAWI;AAXJ,IAYI;AAZJ,IAaI;AAbJ,IAcI;AAdJ,IAeI;AAfJ,IAgBI;AAhBJ,IAiBI;AAjBJ,IAkBI;AAlBJ,IAmBI;AAnBJ,IAoBI;AApBJ,IAqBI;AArBJ,IAsBIC;AAtBJ,IAuBI;AAvBJ,IAwBI;AAxBJ,IAyBI;AAzBJ,IA0BI;AA1BJ,IA2BIC;AA3BJ,IA4BI;AA5BJ,IA6BI;AA7BJ,IA8BI;AA9BJ,IA+BI;AA/BJ,IAgCI;AAhCJ,IAiCI;AAjCJ,IAmCAC,YAAW;AAnCX,IAoCIC,YAAW,KAAK;AApCpB,IAqCI,SAASA,UAAS;AArCtB,IAsCI,kBAAkB;AAtCtB,IAuCI,WAAW;AAvCf,IAwCI,cAAc,SAASC,aAAY,OAAO,MAAM,MAAM;AACxD,MAAI,QAAQ,UAAU,KAAK,MAAM,MAAM,OAAO,GAAG,CAAC,MAAM,YAAY,MAAM,QAAQ,KAAK,IAAI;AAC3F,OAAK,MAAM,OAAO,OAAO,IAAI;AAC7B,SAAO,QAAQ,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,IAAI;AACrD;AA5CA,IA6CI,aAAa,SAASC,YAAW,OAAO,OAAO;AACjD,SAAO,UAAU,CAAC,UAAU,KAAK,KAAK,MAAM,OAAO,GAAG,CAAC,MAAM,YAAY,WAAW,QAAQ,MAAM;AACpG;AA/CA,IAgDI,aAAa,SAASC,cAAa;AACrC,SAAO,YAAY,sBAAsBA,WAAU;AACrD;AAlDA,IAoDA,sBAAsB,SAASC,uBAAsB;AACnD,SAAO,iBAAiB;AAC1B;AAtDA,IAuDI,oBAAoB,SAASC,qBAAoB;AACnD,SAAO,iBAAiB;AAC1B;AAzDA,IA0DI,eAAe,SAASC,cAAa,GAAG;AAC1C,SAAO;AACT;AA5DA,IA6DI,SAAS,SAASC,QAAO,OAAO;AAClC,SAAO,KAAK,MAAM,QAAQ,GAAM,IAAI,OAAU;AAChD;AA/DA,IAgEI,gBAAgB,SAASC,iBAAgB;AAC3C,SAAO,OAAO,WAAW;AAC3B;AAlEA,IAmEIC,YAAW,SAASA,YAAW;AACjC,SAAOpB,SAAQ,cAAc,MAAMA,QAAO,OAAO,SAASA,MAAK,kBAAkBA;AACnF;AArEA,IAsEIqB,eAAc,SAASA,aAAY,GAAG;AACxC,SAAO,CAAC,CAAC,CAACf,OAAM,QAAQ,CAAC;AAC3B;AAxEA,IAyEI,wBAAwB,SAASgB,uBAAsB,mBAAmB;AAC5E,UAAQ,sBAAsB,WAAW,SAASpB,MAAK,UAAU,iBAAiB,MAAME,QAAO,WAAW,iBAAiB,KAAKC,OAAM,WAAW,iBAAiB;AACpK;AA3EA,IA4EI,iBAAiB,SAASkB,gBAAe,SAAS;AACpD,SAAO,cAAc,SAAS,uBAAuB,MAAMF,aAAY,OAAO,IAAI,WAAY;AAC5F,gBAAY,QAAQnB,MAAK;AACzB,gBAAY,SAAS;AACrB,WAAO;AAAA,EACT,IAAI,WAAY;AACd,WAAO,WAAW,OAAO;AAAA,EAC3B;AACF;AApFA,IAqFI,eAAe,SAASsB,cAAa,UAAU,YAAY,MAAM;AACnE,MAAI,IAAI,KAAK,GACT,KAAK,KAAK,IACV,IAAI,KAAK;AACb,UAAQ,IAAI,cAAc,UAAU,uBAAuB,KAAK,WAAY;AAC1E,WAAO,EAAE,EAAE,CAAC;AAAA,EACd,IAAI,WAAY;AACd,YAAQ,aAAa,sBAAsB,EAAE,IAAI,SAAS,WAAW,EAAE,MAAM;AAAA,EAC/E;AACF;AA9FA,IA+FI,kBAAkB,SAASC,iBAAgB,SAAS,YAAY;AAClE,SAAO,CAAC,cAAc,CAAC,SAAS,QAAQ,OAAO,IAAI,eAAe,OAAO,IAAI,WAAY;AACvF,WAAO;AAAA,EACT;AACF;AAnGA,IAoGI,aAAa,SAASC,YAAW,SAAS,OAAO;AACnD,MAAI,IAAI,MAAM,GACV,KAAK,MAAM,IACX,IAAI,MAAM,GACV,IAAI,MAAM;AACd,SAAO,KAAK,IAAI,IAAI,IAAI,WAAW,QAAQ,IAAI,cAAc,SAAS,CAAC,KAAK,EAAE,IAAI,eAAe,OAAO,EAAE,EAAE,CAAC,IAAIL,aAAY,OAAO,KAAKjB,QAAO,CAAC,KAAKC,OAAM,CAAC,KAAK,sBAAsB,EAAE,IAAI,QAAQ,CAAC,IAAI,QAAQ,WAAW,EAAE,CAAC;AACnO;AA1GA,IA2GI,sBAAsB,SAASsB,qBAAoB,MAAM,QAAQ;AACnE,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK,GAAG;AAC/C,KAAC,CAAC,UAAU,CAAC,OAAO,QAAQ,aAAa,IAAI,CAAC,CAAC,MAAM,KAAK,aAAa,CAAC,GAAG,aAAa,IAAI,CAAC,GAAG,aAAa,IAAI,CAAC,CAAC;AAAA,EACrH;AACF;AA/GA,IAgHI,YAAY,SAASC,WAAU,OAAO;AACxC,SAAO,OAAO,UAAU;AAC1B;AAlHA,IAmHI,cAAc,SAASC,aAAY,OAAO;AAC5C,SAAO,OAAO,UAAU;AAC1B;AArHA,IAsHI,YAAY,SAASC,WAAU,OAAO;AACxC,SAAO,OAAO,UAAU;AAC1B;AAxHA,IAyHI,YAAY,SAASC,WAAU,OAAO;AACxC,SAAO,OAAO,UAAU;AAC1B;AA3HA,IA4HI,gBAAgB,SAASC,eAAc,WAAW,UAAU,OAAO;AACrE,SAAO,aAAa,UAAU,SAAS,WAAW,IAAI,CAAC,KAAK,SAAS,UAAU,MAAM;AACvF;AA9HA,IA+HI,YAAY,SAASC,WAAU,MAAM,MAAM;AAC7C,MAAI,KAAK,SAAS;AAChB,QAAI,SAAS,KAAK,OAAO,KAAK,KAAK,IAAI,WAAY;AACjD,aAAO,KAAK,IAAI;AAAA,IAClB,CAAC,IAAI,KAAK,IAAI;AACd,cAAU,OAAO,cAAc,KAAK,oBAAoB;AAAA,EAC1D;AACF;AAtIA,IAuII,OAAO,KAAK;AAvIhB,IAwII,QAAQ;AAxIZ,IAyII,OAAO;AAzIX,IA0II,SAAS;AA1Ib,IA2II,UAAU;AA3Id,IA4II,SAAS;AA5Ib,IA6II,UAAU;AA7Id,IA8II,SAAS;AA9Ib,IA+II,QAAQ;AA/IZ,IAgJI,OAAO;AAhJX,IAiJI,UAAU;AAjJd,IAkJI,WAAW;AAlJf,IAmJI,UAAU;AAnJd,IAoJI,SAAS;AApJb,IAqJI,UAAU;AArJd,IAsJI,MAAM;AAtJV,IAuJI,oBAAoB,SAASC,mBAAkB,SAAS;AAC1D,SAAOhC,MAAK,iBAAiB,OAAO;AACtC;AAzJA,IA0JI,oBAAoB,SAASiC,mBAAkB,SAAS;AAE1D,MAAI,WAAW,kBAAkB,OAAO,EAAE;AAE1C,UAAQ,MAAM,WAAW,aAAa,cAAc,aAAa,UAAU,WAAW;AACxF;AA/JA,IAgKI,eAAe,SAASC,cAAa,KAAK,UAAU;AACtD,WAAS,KAAK,UAAU;AACtB,SAAK,QAAQ,IAAI,CAAC,IAAI,SAAS,CAAC;AAAA,EAClC;AAEA,SAAO;AACT;AAtKA,IAuKI,aAAa,SAASC,YAAW,SAAS,mBAAmB;AAC/D,MAAI,QAAQ,qBAAqB,kBAAkB,OAAO,EAAE,cAAc,MAAM,8BAA8BrC,MAAK,GAAG,SAAS;AAAA,IAC7H,GAAG;AAAA,IACH,GAAG;AAAA,IACH,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,EACT,CAAC,EAAE,SAAS,CAAC,GACT,SAAS,QAAQ,sBAAsB;AAC3C,WAAS,MAAM,SAAS,CAAC,EAAE,KAAK;AAChC,SAAO;AACT;AAvLA,IAwLI,WAAW,SAASsC,UAAS,SAAS,OAAO;AAC/C,MAAI,KAAK,MAAM;AACf,SAAO,QAAQ,WAAW,EAAE,KAAK,QAAQ,WAAW,EAAE,KAAK;AAC7D;AA3LA,IA4LI,sBAAsB,SAASC,qBAAoB,UAAU;AAC/D,MAAI,IAAI,CAAC,GACL,SAAS,SAAS,QAClB,WAAW,SAAS,SAAS,GAC7B;AAEJ,OAAK,KAAK,QAAQ;AAChB,MAAE,KAAK,OAAO,CAAC,IAAI,QAAQ;AAAA,EAC7B;AAEA,SAAO;AACT;AAvMA,IAwMI,mBAAmB,SAASC,kBAAiB,WAAW;AAC1D,SAAO,SAAU,OAAO;AACtB,WAAOxC,MAAK,MAAM,KAAK,oBAAoB,SAAS,GAAG,KAAK;AAAA,EAC9D;AACF;AA5MA,IA6MI,mBAAmB,SAASyC,kBAAiB,sBAAsB;AACrE,MAAI,OAAOzC,MAAK,MAAM,KAAK,oBAAoB,GAC3C,IAAI,MAAM,QAAQ,oBAAoB,KAAK,qBAAqB,MAAM,CAAC,EAAE,KAAK,SAAU0C,IAAG,GAAG;AAChG,WAAOA,KAAI;AAAA,EACb,CAAC;AACD,SAAO,IAAI,SAAU,OAAO,WAAW,WAAW;AAChD,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AAEA,QAAI;AAEJ,QAAI,CAAC,WAAW;AACd,aAAO,KAAK,KAAK;AAAA,IACnB;AAEA,QAAI,YAAY,GAAG;AACjB,eAAS;AAET,WAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC7B,YAAI,EAAE,CAAC,KAAK,OAAO;AACjB,iBAAO,EAAE,CAAC;AAAA,QACZ;AAAA,MACF;AAEA,aAAO,EAAE,IAAI,CAAC;AAAA,IAChB,OAAO;AACL,UAAI,EAAE;AACN,eAAS;AAET,aAAO,KAAK;AACV,YAAI,EAAE,CAAC,KAAK,OAAO;AACjB,iBAAO,EAAE,CAAC;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AAEA,WAAO,EAAE,CAAC;AAAA,EACZ,IAAI,SAAU,OAAO,WAAW,WAAW;AACzC,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AAEA,QAAI,UAAU,KAAK,KAAK;AACxB,WAAO,CAAC,aAAa,KAAK,IAAI,UAAU,KAAK,IAAI,aAAa,UAAU,QAAQ,MAAM,YAAY,IAAI,UAAU,KAAK,YAAY,IAAI,QAAQ,uBAAuB,QAAQ,oBAAoB;AAAA,EAClM;AACF;AA3PA,IA4PI,uBAAuB,SAASC,sBAAqB,UAAU;AACjE,SAAO,SAAU,OAAO,IAAI;AAC1B,WAAO,iBAAiB,oBAAoB,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS;AAAA,EAC5E;AACF;AAhQA,IAiQI,iBAAiB,SAASC,gBAAe,MAAM,SAAS,OAAO,UAAU;AAC3E,SAAO,MAAM,MAAM,GAAG,EAAE,QAAQ,SAAU,MAAM;AAC9C,WAAO,KAAK,SAAS,MAAM,QAAQ;AAAA,EACrC,CAAC;AACH;AArQA,IAsQIC,gBAAe,SAASA,cAAa,SAAS,MAAM,MAAM,YAAY,SAAS;AACjF,SAAO,QAAQ,iBAAiB,MAAM,MAAM;AAAA,IAC1C,SAAS,CAAC;AAAA,IACV,SAAS,CAAC,CAAC;AAAA,EACb,CAAC;AACH;AA3QA,IA4QIC,mBAAkB,SAASA,iBAAgB,SAAS,MAAM,MAAM,SAAS;AAC3E,SAAO,QAAQ,oBAAoB,MAAM,MAAM,CAAC,CAAC,OAAO;AAC1D;AA9QA,IA+QI,iBAAiB,SAASC,gBAAe,MAAM,IAAI,YAAY;AACjE,eAAa,cAAc,WAAW;AAEtC,MAAI,YAAY;AACd,SAAK,IAAI,SAAS,UAAU;AAC5B,SAAK,IAAI,aAAa,UAAU;AAAA,EAClC;AACF;AAtRA,IAuRI,kBAAkB;AAAA,EACpB,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,YAAY;AACd;AA7RA,IA8RI,YAAY;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AACjB;AAjSA,IAkSI,YAAY;AAAA,EACd,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AACT;AAxSA,IAySI,cAAc,SAASC,aAAY,OAAO,MAAM;AAClD,MAAI,UAAU,KAAK,GAAG;AACpB,QAAI,UAAU,MAAM,QAAQ,GAAG,GAC3B,WAAW,CAAC,UAAU,EAAE,MAAM,OAAO,UAAU,CAAC,IAAI,KAAK,WAAW,MAAM,OAAO,UAAU,CAAC,CAAC,IAAI;AAErG,QAAI,CAAC,SAAS;AACZ,YAAM,QAAQ,GAAG,IAAI,YAAY,YAAY,OAAO;AACpD,cAAQ,MAAM,OAAO,GAAG,UAAU,CAAC;AAAA,IACrC;AAEA,YAAQ,YAAY,SAAS,YAAY,UAAU,KAAK,IAAI,OAAO,CAAC,MAAM,QAAQ,GAAG,IAAI,WAAW,KAAK,IAAI,OAAO,MAAM,WAAW,KAAK,KAAK;AAAA,EACjJ;AAEA,SAAO;AACT;AAvTA,IAwTI,gBAAgB,SAASC,eAAc,MAAM,MAAM,WAAW,WAAW,OAAO,QAAQ,cAAc,oBAAoB;AAC5H,MAAI,aAAa,MAAM,YACnB,WAAW,MAAM,UACjB,WAAW,MAAM,UACjB,SAAS,MAAM,QACf,aAAa,MAAM;AAEvB,MAAI,IAAI9C,MAAK,cAAc,KAAK,GAC5B,mBAAmBkB,aAAY,SAAS,KAAK,cAAc,WAAW,SAAS,MAAM,SACrF,aAAa,KAAK,QAAQ,UAAU,MAAM,IAC1C,SAAS,mBAAmBhB,SAAQ,WACpC,UAAU,KAAK,QAAQ,OAAO,MAAM,IACpC,QAAQ,UAAU,aAAa,UAC/B,MAAM,kBAAkB,QAAQ,gBAAgB,WAAW,YAAY,QAAQ,kBAAkB,aAAa;AAElH,SAAO,gBAAgB,cAAc,uBAAuB,mBAAmB,WAAW;AAC1F,GAAC,cAAc,sBAAsB,CAAC,sBAAsB,QAAQ,cAAc,YAAY,SAAS,WAAW,OAAO,SAAS,WAAW,MAAM,KAAK;AACxJ,mBAAiB,OAAO,iDAAiD,aAAa,cAAc;AACpG,IAAE,WAAW;AACb,IAAE,aAAa,SAAS,iBAAiB,QAAQ,OAAO,aAAa,OAAO,GAAG;AAC/E,IAAE,MAAM,UAAU;AAClB,IAAE,YAAY,QAAQ,SAAS,IAAI,OAAO,MAAM,OAAO;AACvD,SAAO,SAAS,CAAC,IAAI,OAAO,aAAa,GAAG,OAAO,SAAS,CAAC,CAAC,IAAI,OAAO,YAAY,CAAC;AACtF,IAAE,UAAU,EAAE,WAAW,UAAU,GAAG,EAAE;AAExC,kBAAgB,GAAG,GAAG,WAAW,OAAO;AAExC,SAAO;AACT;AApVA,IAqVI,kBAAkB,SAAS6C,iBAAgB,QAAQ,OAAO,WAAW,SAAS;AAChF,MAAI,OAAO;AAAA,IACT,SAAS;AAAA,EACX,GACI,OAAO,UAAU,UAAU,QAAQ,IAAI,GACvC,eAAe,UAAU,UAAU,OAAO,KAAK;AACnD,SAAO,aAAa;AACpB,OAAK,UAAU,IAAI,SAAS,IAAI,UAAU,OAAO;AACjD,OAAK,UAAU,CAAC,IAAI,UAAU,QAAQ;AACtC,OAAK,WAAW,OAAO,MAAM,IAAI;AACjC,OAAK,WAAW,eAAe,MAAM,IAAI;AACzC,OAAK,UAAU,CAAC,IAAI,QAAQ;AAC5B,EAAAlD,MAAK,IAAI,QAAQ,IAAI;AACvB;AAlWA,IAmWI,YAAY,CAAC;AAnWjB,IAoWI,OAAO,CAAC;AApWZ,IAqWI;AArWJ,IAsWI,QAAQ,SAASmD,SAAQ;AAC3B,SAAOxC,UAAS,IAAI,kBAAkB,OAAO,WAAW,SAAS,sBAAsB,UAAU;AACnG;AAxWA,IAyWIyC,aAAY,SAASA,aAAY;AAEnC,MAAI,CAAC5C,gBAAe,CAACA,aAAY,aAAaA,aAAY,SAASH,OAAM,aAAa;AAEpF,eAAW;AAEX,QAAIG,cAAa;AACf,iBAAW,SAAS,sBAAsB,UAAU;AAAA,IACtD,OAAO;AACL,iBAAW;AAAA,IAEb;AAEA,uBAAmB,UAAU,aAAa;AAC1C,sBAAkBG,UAAS;AAAA,EAC7B;AACF;AAzXA,IA0XI,qBAAqB,SAAS0C,sBAAqB;AACrD,qBAAmBnD,MAAK;AACxB,sBAAoBA,MAAK;AAC3B;AA7XA,IA8XI,YAAY,SAASoD,WAAU,OAAO;AACxC,aAAW;AACX,GAAC,UAAU,QAAQ,CAAC,eAAe,CAAC,iBAAiB,CAACnD,MAAK,qBAAqB,CAACA,MAAK,4BAA4B,CAAC,uBAAuB,qBAAqBD,MAAK,cAAc,KAAK,IAAIA,MAAK,cAAc,iBAAiB,IAAIA,MAAK,cAAc,UAAU,aAAa,QAAQ,IAAI;AAC3R;AAjYA,IAmYA,aAAa,CAAC;AAnYd,IAoYI,cAAc,CAAC;AApYnB,IAqYI,eAAe,SAASqD,gBAAe;AACzC,SAAOT,iBAAgBU,gBAAe,aAAaD,aAAY,KAAK,YAAY,IAAI;AACtF;AAvYA,IAwYI,YAAY,SAASE,WAAU,MAAM;AACvC,SAAO,WAAW,IAAI,KAAK,WAAW,IAAI,EAAE,IAAI,SAAU,GAAG;AAC3D,WAAO,EAAE;AAAA,EACX,CAAC,KAAK;AACR;AA5YA,IA6YI,eAAe,CAAC;AA7YpB,IA+YA,kBAAkB,SAASC,iBAAgB,OAAO;AAChD,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK,GAAG;AAC/C,QAAI,CAAC,SAAS,aAAa,IAAI,CAAC,KAAK,aAAa,IAAI,CAAC,EAAE,UAAU,OAAO;AACxE,mBAAa,CAAC,EAAE,MAAM,UAAU,aAAa,IAAI,CAAC;AAClD,mBAAa,CAAC,EAAE,WAAW,aAAa,CAAC,EAAE,aAAa,aAAa,aAAa,IAAI,CAAC,KAAK,EAAE;AAC9F,mBAAa,IAAI,CAAC,EAAE,UAAU;AAAA,IAChC;AAAA,EACF;AACF;AAvZA,IAwZI,aAAa,SAASC,YAAW,MAAM,OAAO;AAChD,MAAI;AAEJ,OAAK,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AACxC,cAAU,UAAU,EAAE;AAEtB,QAAI,YAAY,CAAC,SAAS,QAAQ,SAAS,QAAQ;AACjD,UAAI,MAAM;AACR,gBAAQ,KAAK,CAAC;AAAA,MAChB,OAAO;AACL,gBAAQ,OAAO,MAAM,IAAI;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAEA,gBAAc;AACd,WAAS,gBAAgB,KAAK;AAC9B,WAAS,UAAU,QAAQ;AAC7B;AA1aA,IA2aI,qBAAqB,SAASC,oBAAmB,mBAAmB,OAAO;AAE7E,aAAW;AACX,GAAC,SAAS,CAAC,mBAAmB,WAAW,QAAQ,SAAU,KAAK;AAC9D,WAAO,YAAY,GAAG,KAAK,IAAI,cAAc,IAAI,MAAM;AAAA,EACzD,CAAC;AACD,YAAU,iBAAiB,MAAM1D,MAAK,QAAQ,oBAAoB,qBAAqB;AACzF;AAlbA,IAmbI;AAnbJ,IAobI,aAAa;AApbjB,IAqbI;AArbJ,IAsbI,mBAAmB,SAAS2D,oBAAmB;AAEjD,MAAI,oBAAoB,YAAY;AAClC,QAAI,KAAK,kBAAkB;AAC3B,0BAAsB,WAAY;AAChC,aAAO,OAAO,cAAc,YAAY,IAAI;AAAA,IAC9C,CAAC;AAAA,EACH;AACF;AA9bA,IA+bI,gBAAgB,SAASC,iBAAgB;AAC3C,EAAAzD,OAAM,YAAY,SAAS;AAE3B,WAAS,CAACG,gBAAe,UAAU,gBAAgBN,MAAK;AAExD,EAAAG,OAAM,YAAY,SAAS;AAC7B;AArcA,IAscI,kBAAkB,SAAS0D,iBAAgB,MAAM;AACnD,SAAO,SAAS,8FAA8F,EAAE,QAAQ,SAAU,IAAI;AACpI,WAAO,GAAG,MAAM,UAAU,OAAO,SAAS;AAAA,EAC5C,CAAC;AACH;AA1cA,IA2cI,cAAc,SAASC,aAAY,OAAO,YAAY;AACxD,EAAA5D,UAASD,MAAK;AAEd,EAAAE,SAAQF,MAAK;AACb,EAAAG,SAAQ,CAACJ,OAAMC,OAAMC,SAAQC,MAAK;AAElC,MAAI,mBAAmB,CAAC,SAAS,CAAC,aAAa;AAC7C,IAAAwC,cAAaW,gBAAe,aAAa,YAAY;AAErD;AAAA,EACF;AAEA,gBAAc;AAEd,mBAAiBA,eAAc,eAAe;AAE9C,aAAW,QAAQ,SAAU,KAAK;AAChC,WAAO,YAAY,GAAG,KAAK,EAAE,IAAI,YAAY,IAAI,MAAM,IAAI;AAAA,EAC7D,CAAC;AAGD,MAAI,eAAe,UAAU,aAAa;AAE1C,WAASA,eAAc,KAAK;AAC5B,gBAAc,WAAW;AAEzB,aAAW,QAAQ,SAAU,KAAK;AAChC,QAAI,YAAY,GAAG,GAAG;AACpB,UAAI,WAAW,IAAI,OAAO,MAAM,iBAAiB;AAEjD,UAAI,CAAC;AAAA,IACP;AAAA,EACF,CAAC;AAED,YAAU,MAAM,CAAC,EAAE,QAAQ,SAAU,GAAG;AACtC,WAAO,EAAE,QAAQ;AAAA,EACnB,CAAC;AAGD,gBAAc;AAEd,YAAU,QAAQ,SAAU,GAAG;AAE7B,QAAI,EAAE,iBAAiB,EAAE,KAAK;AAC5B,UAAI,OAAO,EAAE,KAAK,aAAa,gBAAgB,gBAC3C,WAAW,EAAE,IAAI,IAAI;AACzB,QAAE,OAAO,MAAM,CAAC;AAChB,QAAE,iBAAiB,EAAE,IAAI,IAAI,IAAI,QAAQ;AACzC,QAAE,QAAQ;AAAA,IACZ;AAAA,EACF,CAAC;AAED,iBAAe;AAEf,kBAAgB,IAAI;AAEpB,YAAU,QAAQ,SAAU,GAAG;AAE7B,QAAI,MAAM,WAAW,EAAE,UAAU,EAAE,IAAI,GACnC,WAAW,EAAE,KAAK,QAAQ,SAAS,EAAE,aAAa,EAAE,MAAM,KAC1D,aAAa,EAAE,eAAe,EAAE,SAAS;AAE7C,KAAC,YAAY,eAAe,EAAE,aAAa,aAAa,MAAM,IAAI,EAAE,OAAO,WAAW,KAAK,IAAI,aAAa,MAAM,EAAE,QAAQ,GAAG,GAAG,IAAI,EAAE,KAAK,IAAI;AAAA,EACnJ,CAAC;AAED,kBAAgB,KAAK;AAErB,iBAAe;AACf,eAAa,QAAQ,SAAU,QAAQ;AACrC,WAAO,UAAU,OAAO,UAAU,OAAO,OAAO,EAAE;AAAA,EACpD,CAAC;AAED,aAAW,QAAQ,SAAU,KAAK;AAChC,QAAI,YAAY,GAAG,GAAG;AACpB,UAAI,UAAU,sBAAsB,WAAY;AAC9C,eAAO,IAAI,OAAO,MAAM,iBAAiB;AAAA,MAC3C,CAAC;AACD,UAAI,OAAO,IAAI,IAAI,GAAG;AAAA,IACxB;AAAA,EACF,CAAC;AAED,qBAAmB,oBAAoB,CAAC;AAExC,eAAa,MAAM;AAEnB;AACA,mBAAiB;AAEjB,aAAW,CAAC;AAEZ,YAAU,QAAQ,SAAU,GAAG;AAC7B,WAAO,YAAY,EAAE,KAAK,SAAS,KAAK,EAAE,KAAK,UAAU,CAAC;AAAA,EAC5D,CAAC;AAED,mBAAiBA,eAAc,eAAe;AAE9C,YAAU,SAAS;AACrB;AA5iBA,IA6iBI,cAAc;AA7iBlB,IA8iBI,aAAa;AA9iBjB,IA+iBI;AA/iBJ,IAgjBI,aAAa,SAASS,YAAW,OAAO;AAC1C,MAAI,UAAU,KAAK,CAAC,kBAAkB,CAAC,aAAa;AAElD,IAAAT,eAAc,aAAa;AAC3B,gBAAY,SAAS,OAAO,CAAC;AAE7B,QAAI,IAAI,UAAU,QACd,OAAO7C,UAAS,GAChB,iBAAiB,OAAO,UAAU,IAClC,SAAS,KAAK,UAAU,CAAC,EAAE,OAAO;AAEtC,iBAAa,cAAc,SAAS,KAAK;AACzC,uBAAmB,cAAc;AAEjC,QAAI,gBAAgB;AAClB,UAAI,mBAAmB,CAAC,kBAAkB,OAAO,kBAAkB,KAAK;AACtE,0BAAkB;AAElB,kBAAU,WAAW;AAAA,MACvB;AAEA,eAAS;AACT,eAAS;AAAA,IACX;AAEA,QAAI,aAAa,GAAG;AAClB,WAAK;AAEL,aAAO,OAAO,GAAG;AACf,kBAAU,EAAE,KAAK,UAAU,EAAE,EAAE,OAAO,GAAG,cAAc;AAAA,MACzD;AAEA,mBAAa;AAAA,IACf,OAAO;AACL,WAAK,KAAK,GAAG,KAAK,GAAG,MAAM;AACzB,kBAAU,EAAE,KAAK,UAAU,EAAE,EAAE,OAAO,GAAG,cAAc;AAAA,MACzD;AAAA,IACF;AAEA,IAAA6C,eAAc,aAAa;AAAA,EAC7B;AAEA,WAAS;AACX;AA3lBA,IA4lBI,mBAAmB,CAAC,OAAO,MAAM,SAAS,QAAQ,UAAU,SAAS,UAAU,QAAQ,UAAU,MAAM,UAAU,OAAO,WAAW,cAAc,SAAS,UAAU,mBAAmB,iBAAiB,gBAAgB,cAAc,YAAY,eAAe,aAAa,aAAa,OAAO;AA5lBtS,IA6lBI,cAAc,iBAAiB,OAAO,CAAC,QAAQ,SAAS,aAAa,QAAQ,QAAQ,QAAQ,SAAS,YAAY,SAAS,UAAU,WAAW,MAAM,WAAW,QAAQ,WAAW,SAAS,WAAW,KAAK,CAAC;AA7lBlN,IA8lBI,cAAc,SAASU,aAAY,KAAK,QAAQ,OAAO;AACzD,YAAU,KAAK;AAEf,MAAI,QAAQ,IAAI;AAEhB,MAAI,MAAM,gBAAgB;AACxB,cAAU,MAAM,WAAW;AAAA,EAC7B,WAAW,IAAI,MAAM,WAAW;AAC9B,QAAI,SAAS,OAAO;AAEpB,QAAI,QAAQ;AACV,aAAO,aAAa,KAAK,MAAM;AAC/B,aAAO,YAAY,MAAM;AAAA,IAC3B;AAAA,EACF;AAEA,MAAI,MAAM,YAAY;AACxB;AA/mBA,IAgnBI,aAAa,SAASC,YAAW,KAAK,QAAQ,IAAI,aAAa;AACjE,MAAI,CAAC,IAAI,MAAM,WAAW;AACxB,QAAI,IAAI,iBAAiB,QACrB,cAAc,OAAO,OACrB,WAAW,IAAI,OACf;AAEJ,WAAO,KAAK;AACV,UAAI,iBAAiB,CAAC;AACtB,kBAAY,CAAC,IAAI,GAAG,CAAC;AAAA,IACvB;AAEA,gBAAY,WAAW,GAAG,aAAa,aAAa,aAAa;AACjE,OAAG,YAAY,aAAa,YAAY,UAAU;AAClD,aAAS,OAAO,IAAI,SAAS,MAAM,IAAI;AACvC,gBAAY,YAAY,GAAG,aAAa;AACxC,gBAAY,WAAW;AACvB,gBAAY,YAAY;AACxB,gBAAY,MAAM,IAAI,SAAS,KAAK,WAAW,IAAI;AACnD,gBAAY,OAAO,IAAI,SAAS,KAAK,SAAS,IAAI;AAClD,gBAAY,QAAQ,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,IAAI,SAAS,KAAK,IAAI;AAE/E,cAAU,WAAW;AAErB,aAAS,MAAM,IAAI,SAAS,QAAQ,MAAM,IAAI,GAAG,MAAM;AACvD,aAAS,OAAO,IAAI,SAAS,QAAQ,OAAO,IAAI,GAAG,OAAO;AAC1D,aAAS,QAAQ,IAAI,GAAG,QAAQ;AAEhC,QAAI,IAAI,eAAe,QAAQ;AAC7B,UAAI,WAAW,aAAa,QAAQ,GAAG;AACvC,aAAO,YAAY,GAAG;AAAA,IACxB;AAEA,QAAI,MAAM,YAAY;AAAA,EACxB;AACF;AAnpBA,IAopBI,WAAW;AAppBf,IAqpBI,YAAY,SAASC,WAAU,OAAO;AACxC,MAAI,OAAO;AACT,QAAI,QAAQ,MAAM,EAAE,OAChB,IAAI,MAAM,QACV,IAAI,GACJ,GACA;AACJ,KAAC,MAAM,EAAE,SAASpE,MAAK,KAAK,SAAS,MAAM,CAAC,GAAG,UAAU;AAEzD,WAAO,IAAI,GAAG,KAAK,GAAG;AACpB,cAAQ,MAAM,IAAI,CAAC;AACnB,UAAI,MAAM,CAAC;AAEX,UAAI,OAAO;AACT,cAAM,CAAC,IAAI;AAAA,MACb,WAAW,MAAM,CAAC,GAAG;AACnB,cAAM,eAAe,EAAE,QAAQ,UAAU,KAAK,EAAE,YAAY,CAAC;AAAA,MAC/D;AAAA,IACF;AAAA,EACF;AACF;AAzqBA,IA0qBI,YAAY,SAASqE,WAAU,SAAS;AAE1C,MAAI,IAAI,YAAY,QAChB,QAAQ,QAAQ,OAChB,QAAQ,CAAC,GACT,IAAI;AAER,SAAO,IAAI,GAAG,KAAK;AACjB,UAAM,KAAK,YAAY,CAAC,GAAG,MAAM,YAAY,CAAC,CAAC,CAAC;AAAA,EAClD;AAEA,QAAM,IAAI;AACV,SAAO;AACT;AAvrBA,IAwrBI,aAAa,SAASC,YAAW,OAAO,UAAU,aAAa;AACjE,MAAI,SAAS,CAAC,GACV,IAAI,MAAM,QACV,IAAI,cAAc,IAAI,GAE1B;AAEA,SAAO,IAAI,GAAG,KAAK,GAAG;AACpB,QAAI,MAAM,CAAC;AACX,WAAO,KAAK,GAAG,KAAK,WAAW,SAAS,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC;AAAA,EAC3D;AAEA,SAAO,IAAI,MAAM;AACjB,SAAO;AACT;AAtsBA,IAusBI,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,KAAK;AACP;AA1sBA,IAmtBA,iBAAiB,SAASC,gBAAe,OAAO,SAAS,cAAc,WAAW,QAAQ,QAAQ,gBAAgB,MAAM,gBAAgB,aAAa,kBAAkB,aAAa,oBAAoB,eAAe;AACrN,cAAY,KAAK,MAAM,QAAQ,MAAM,IAAI;AAEzC,MAAI,UAAU,KAAK,KAAK,MAAM,OAAO,GAAG,CAAC,MAAM,OAAO;AACpD,YAAQ,eAAe,MAAM,OAAO,CAAC,MAAM,MAAM,YAAY,MAAM,MAAM,OAAO,CAAC,GAAG,YAAY,IAAI;AAAA,EACtG;AAEA,MAAI,OAAO,qBAAqB,mBAAmB,KAAK,IAAI,GACxD,IACA,IACA;AACJ,wBAAsB,mBAAmB,KAAK,CAAC;AAC/C,QAAM,KAAK,MAAM,QAAQ,CAAC;AAE1B,MAAI,CAAC,UAAU,KAAK,GAAG;AACrB,gBAAY,OAAO,MAAM,UAAU,QAAQ,IAAI;AAC/C,QAAI,WAAW,SAAS,KAAK,MAAM,GAAG,GAClC,QACA,aACA,cACA;AACJ,cAAU,WAAW,SAAS,IAAI,KAAKlE;AACvC,aAAS,WAAW,OAAO,KAAK,CAAC;AAEjC,SAAK,CAAC,UAAU,CAAC,OAAO,QAAQ,CAAC,OAAO,QAAQ,kBAAkB,OAAO,EAAE,YAAY,QAAQ;AAE7F,gBAAU,QAAQ,MAAM;AACxB,cAAQ,MAAM,UAAU;AACxB,eAAS,WAAW,OAAO;AAC3B,gBAAU,QAAQ,MAAM,UAAU,UAAU,QAAQ,MAAM,eAAe,SAAS;AAAA,IACpF;AAEA,kBAAc,YAAY,QAAQ,CAAC,GAAG,OAAO,UAAU,CAAC,CAAC;AACzD,mBAAe,YAAY,QAAQ,CAAC,KAAK,KAAK,YAAY;AAC1D,YAAQ,OAAO,UAAU,CAAC,IAAI,eAAe,UAAU,CAAC,IAAI,cAAc,cAAc,SAAS;AACjG,sBAAkB,gBAAgB,gBAAgB,cAAc,WAAW,eAAe,eAAe,MAAM,eAAe,YAAY,eAAe,EAAE;AAC3J,oBAAgB,eAAe;AAAA,EACjC,OAAO;AACL,2BAAuB,QAAQL,MAAK,MAAM,SAAS,mBAAmB,cAAc,OAAO,mBAAmB,cAAc,KAAK,GAAG,aAAa,KAAK;AACtJ,sBAAkB,gBAAgB,gBAAgB,cAAc,WAAW,IAAI;AAAA,EACjF;AAEA,MAAI,eAAe;AACjB,SAAK,aAAa,IAAI,SAAS;AAC/B,YAAQ,MAAM,QAAQ;AAAA,EACxB;AAEA,MAAI,QAAQ;AACV,QAAI,WAAW,QAAQ,cACnB,UAAU,OAAO;AACrB,SAAK,WAAW,UAAU;AAE1B,oBAAgB,QAAQ,UAAU,WAAW,WAAW,WAAW,MAAM,CAAC,YAAY,mBAAmB,KAAK,IAAIK,OAAM,EAAE,GAAGD,QAAO,EAAE,CAAC,IAAI,OAAO,WAAW,EAAE,MAAM,WAAW,CAAC;AAEjL,QAAI,kBAAkB;AACpB,uBAAiB,WAAW,cAAc;AAC1C,2BAAqB,OAAO,MAAM,UAAU,GAAG,CAAC,IAAI,eAAe,UAAU,GAAG,CAAC,IAAI,UAAU,GAAG,IAAI,OAAO,UAAU;AAAA,IACzH;AAAA,EACF;AAEA,MAAI,sBAAsB,SAAS;AACjC,SAAK,WAAW,OAAO;AACvB,uBAAmB,KAAK,WAAW;AACnC,SAAK,WAAW,OAAO;AACvB,uBAAmB,gBAAgB,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC;AACnE,YAAQ,QAAQ,mBAAmB,gBAAgB;AAAA,EACrD;AAEA,wBAAsB,mBAAmB,KAAK,IAAI;AAClD,SAAO,qBAAqB,QAAQ,KAAK,MAAM,KAAK;AACtD;AAzxBA,IA0xBI,aAAa;AA1xBjB,IA2xBI,YAAY,SAASoE,WAAU,SAAS,QAAQ,KAAK,MAAM;AAC7D,MAAI,QAAQ,eAAe,QAAQ;AACjC,QAAI,QAAQ,QAAQ,OAChB,GACA;AAEJ,QAAI,WAAWnE,QAAO;AACpB,cAAQ,UAAU,MAAM;AAExB,WAAK,kBAAkB,OAAO;AAE9B,WAAK,KAAK,IAAI;AAEZ,YAAI,CAAC,CAAC,KAAK,CAAC,WAAW,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,OAAO,MAAM,CAAC,MAAM,YAAY,MAAM,KAAK;AACpF,gBAAM,CAAC,IAAI,GAAG,CAAC;AAAA,QACjB;AAAA,MACF;AAEA,YAAM,MAAM;AACZ,YAAM,OAAO;AAAA,IACf,OAAO;AACL,YAAM,UAAU,QAAQ;AAAA,IAC1B;AAEA,IAAAL,MAAK,KAAK,SAAS,OAAO,EAAE,UAAU;AACtC,WAAO,YAAY,OAAO;AAAA,EAC5B;AACF;AAtzBA,IAuzBI,uBAAuB,SAASyE,sBAAqB,cAAc,cAAc,aAAa;AAChG,MAAI,QAAQ,cACR,QAAQ;AACZ,SAAO,SAAU,OAAO;AACtB,QAAI,UAAU,KAAK,MAAM,aAAa,CAAC;AAEvC,QAAI,YAAY,SAAS,YAAY,SAAS,KAAK,IAAI,UAAU,KAAK,IAAI,KAAK,KAAK,IAAI,UAAU,KAAK,IAAI,GAAG;AAE5G,cAAQ;AACR,qBAAe,YAAY;AAAA,IAC7B;AAEA,YAAQ;AACR,YAAQ,KAAK,MAAM,KAAK;AACxB,WAAO;AAAA,EACT;AACF;AAv0BA,IAw0BI,eAAe,SAASC,cAAa,QAAQ,WAAW,OAAO;AACjE,MAAI,OAAO,CAAC;AACZ,OAAK,UAAU,CAAC,IAAI,OAAO;AAC3B,EAAA1E,MAAK,IAAI,QAAQ,IAAI;AACvB;AA50BA,IAo1BA,mBAAmB,SAAS2E,kBAAiB,UAAU,WAAW;AAChE,MAAI,YAAY,eAAe,UAAU,SAAS,GAC9C,OAAO,YAAY,UAAU,IAEjC,WAAW,SAASC,UAAS,UAAU,MAAM,cAAc,SAAS,SAAS;AAC3E,QAAI,QAAQA,UAAS,OACjB,aAAa,KAAK,YAClB,YAAY,CAAC;AACjB,mBAAe,gBAAgB,UAAU;AAEzC,QAAI,uBAAuB,qBAAqB,WAAW,cAAc,WAAY;AACnF,YAAM,KAAK;AACX,MAAAA,UAAS,QAAQ;AAAA,IACnB,CAAC;AAED,cAAU,WAAW,WAAW;AAEhC,cAAU,WAAW,WAAW;AAChC,aAAS,MAAM,KAAK;AACpB,SAAK,IAAI,IAAI;AACb,SAAK,UAAU;AACf,SAAK,YAAY;AAEjB,cAAU,IAAI,IAAI,WAAY;AAC5B,aAAO,qBAAqB,eAAe,UAAU,MAAM,QAAQ,UAAU,MAAM,QAAQ,MAAM,KAAK;AAAA,IACxG;AAEA,SAAK,WAAW,WAAY;AAC1B,iBAAW;AACX,MAAAA,UAAS,SAAS,WAAW;AAAA,IAC/B;AAEA,SAAK,aAAa,WAAY;AAC5B,MAAAA,UAAS,QAAQ;AACjB,oBAAc,WAAW,KAAK,KAAK;AAAA,IACrC;AAEA,YAAQA,UAAS,QAAQ5E,MAAK,GAAG,UAAU,IAAI;AAC/C,WAAO;AAAA,EACT;AAEA,WAAS,IAAI,IAAI;AAEjB,YAAU,eAAe,WAAY;AACnC,WAAO,SAAS,SAAS,SAAS,MAAM,KAAK,MAAM,SAAS,QAAQ;AAAA,EACtE;AAEA,EAAA6C,cAAa,UAAU,SAAS,UAAU,YAAY;AAGtD,EAAAW,eAAc,WAAWX,cAAa,UAAU,aAAa,UAAU,YAAY;AACnF,SAAO;AACT;AAEO,IAAIW,iBAA6B,WAAY;AAClD,WAASA,eAAc,MAAM,WAAW;AACtC,IAAAvD,iBAAgBuD,eAAc,SAASxD,KAAI,KAAK,QAAQ,KAAK,2CAA2C;AAExG,IAAAS,UAAS,IAAI;AAEb,SAAK,KAAK,MAAM,SAAS;AAAA,EAC3B;AAEA,MAAI,SAAS+C,eAAc;AAE3B,SAAO,OAAO,SAAS,KAAK,MAAM,WAAW;AAC3C,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,QAAQ,KAAK,KAAK,MAAM,IAAI;AAEjC,QAAI,CAAC,UAAU;AACb,WAAK,SAAS,KAAK,UAAU,KAAK,OAAO;AACzC;AAAA,IACF;AAEA,WAAO,aAAa,UAAU,IAAI,KAAK,UAAU,IAAI,KAAK,KAAK,WAAW;AAAA,MACxE,SAAS;AAAA,IACX,IAAI,MAAM,SAAS;AAEnB,QAAI,QAAQ,MACR,WAAW,MAAM,UACjB,cAAc,MAAM,aACpB,KAAK,MAAM,IACX,WAAW,MAAM,UACjB,YAAY,MAAM,WAClB,QAAQ,MAAM,OACd,UAAU,MAAM,SAChB,MAAM,MAAM,KACZ,aAAa,MAAM,YACnB,sBAAsB,MAAM,qBAC5B,gBAAgB,MAAM,eACtB,kBAAkB,MAAM,iBACxB,iBAAiB,MAAM,gBACvB,OAAO,MAAM,MACb,OAAO,MAAM,MACb,cAAc,MAAM,aACpB,YAAY,MAAM,WAClB,qBAAqB,MAAM,oBAC3B,gBAAgB,MAAM,eACtB,kBAAkB,MAAM,iBACxB,YAAY,KAAK,cAAc,KAAK,sBAAsB,KAAK,eAAe,QAAQ,cAAc,WACpG,WAAW,CAAC,SAAS,UAAU,GAC/B,WAAW,WAAW,KAAK,YAAYtD,KAAI,GAC3C,gBAAgBF,MAAK,KAAK,SAAS,QAAQ,GAC3C,aAAaqB,aAAY,QAAQ,GACjC,oBAAoB,aAAa,OAAO,KAAK,UAAU,cAAc,UAAU,SAAS,KAAK,cAAc,aAAa,SACxH,YAAY,CAAC,KAAK,SAAS,KAAK,SAAS,KAAK,aAAa,KAAK,WAAW,GAC3E,gBAAgB,YAAY,KAAK,cAAc,MAAM,GAAG,GACxD,UAAU,aAAa,OAAO,KAAK,UAAU,UAAU,SACvD,cAAc,aAAa,IAAI,WAAW,kBAAkB,QAAQ,EAAE,WAAW,UAAU,KAAK,MAAM,CAAC,KAAK,GAC5G,OAAO,MACP,gBAAgB,KAAK,iBAAiB,WAAY;AACpD,aAAO,KAAK,cAAc,IAAI;AAAA,IAChC,GACI,kBAAkB,aAAa,UAAU,YAAY,SAAS,GAC9D,qBAAqB,gBAAgB,UAAU,UAAU,GACzD,WAAW,GACX,cAAc,GACd,eAAe,GACf,aAAa,eAAe,UAAU,SAAS,GAC/C,SACA,UACA,UACA,SACA,SACA,OACA,KACA,aACA,WACA,oBACA,kBACA,YACA,oBACA,QACA,kBACA,gBACA,UACA,QACA,QACA,WACA,WACA,UACA,WACA,cACA,aACA,mBACA,UACA,iBACA,IACA,OACA,OACA,YACA,aACA,cACA,iBACA,YACA,kBACA,gBACA;AAGJ,SAAK,cAAc,KAAK,YAAY;AACpC,SAAK,OAAO;AACZ,qBAAiB;AACjB,SAAK,WAAW;AAChB,SAAK,SAAS,qBAAqB,mBAAmB,KAAK,KAAK,kBAAkB,IAAI;AACtF,cAAU,WAAW;AACrB,SAAK,OAAO;AACZ,gBAAY,aAAa,KAAK;AAE9B,QAAI,qBAAqB,MAAM;AAC7B,cAAQ;AACR,WAAK,oBAAoB,UAAU,WAAW;AAAA,IAChD;AAEA,kBAAc,cAAc,cAAc,eAAe;AAAA,MACvD,KAAK,iBAAiB,UAAU,SAAS;AAAA,MACzC,MAAM,iBAAiB,UAAU,WAAW;AAAA,IAC9C;AACA,SAAK,UAAU,UAAU,cAAc,YAAY,UAAU,CAAC;AAE9D,SAAK,gBAAgB,SAAU,OAAO;AACpC,oBAAc,UAAU,KAAK,KAAK;AAElC,UAAI,CAAC,aAAa;AAChB,sBAAc,WAAW,SAAS,CAAC,EAAE,KAAK;AAC1C,qBAAa;AAAA,MACf,OAAO;AACL,qBAAa,WAAW,SAAS,KAAK,IAAI,aAAarB,MAAK,GAAG,WAAW;AAAA,UACxE,MAAM;AAAA,UACN,eAAe;AAAA,UACf,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,YAAY,SAAS,aAAa;AAChC,mBAAO,mBAAmB,gBAAgB,IAAI;AAAA,UAChD;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,QAAI,WAAW;AACb,gBAAU,KAAK,OAAO;AACtB,gBAAU,YAAY,CAAC,KAAK,cAAc,UAAU,KAAK,oBAAoB,SAAS,KAAK,oBAAoB,SAAS,UAAU,SAAS,KAAK,UAAU,OAAO,GAAG,MAAM,IAAI;AAE9K,WAAK,YAAY,UAAU,MAAM;AACjC,gBAAU,gBAAgB;AAC1B,WAAK,cAAc,KAAK;AACxB,cAAQ;AACR,aAAO,KAAK,UAAU,KAAK;AAAA,IAC7B;AAEA,QAAI,MAAM;AAER,UAAI,CAAC,UAAU,IAAI,KAAK,KAAK,MAAM;AACjC,eAAO;AAAA,UACL,QAAQ;AAAA,QACV;AAAA,MACF;AAEA,0BAAoBK,OAAM,SAASL,MAAK,IAAI,aAAa,CAACK,QAAOD,OAAM,IAAI,UAAU;AAAA,QACnF,gBAAgB;AAAA,MAClB,CAAC;AAED,iBAAW,QAAQ,SAAU,GAAG;AAC9B,eAAO,YAAY,CAAC,KAAK,EAAE,YAAY,aAAaD,MAAK,oBAAoBC,UAAS,cAAc,EAAE,SAAS;AAAA,MACjH,CAAC;AAGD,iBAAW,YAAY,KAAK,MAAM,IAAI,KAAK,SAAS,KAAK,WAAW,WAAW,iBAAiB,SAAS,IAAI,KAAK,WAAW,sBAAsB,qBAAqB,SAAS,IAAI,KAAK,gBAAgB,QAAQ,SAAU,OAAO,IAAI;AACrO,eAAO,iBAAiB,KAAK,MAAM,EAAE,OAAOO,UAAS,IAAI,cAAc,MAAM,IAAI,GAAG,SAAS;AAAA,MAC/F,IAAIX,MAAK,MAAM,KAAK,KAAK,MAAM;AAC/B,qBAAe,KAAK,YAAY;AAAA,QAC9B,KAAK;AAAA,QACL,KAAK;AAAA,MACP;AACA,qBAAe,UAAU,YAAY,IAAIO,QAAO,aAAa,KAAK,aAAa,GAAG,IAAIA,QAAO,cAAc,YAAY;AACvH,wBAAkBP,MAAK,YAAY,KAAK,SAAS,cAAc,KAAK,KAAK,WAAY;AACnF,YAAI,SAAS,WAAW,GACpB,oBAAoBW,UAAS,IAAI,cAAc,KAC/C,QAAQ,QAAQ;AAEpB,aAAK,qBAAqB,KAAK,IAAI,KAAK,YAAY,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,kBAAkB,aAAa,QAAQ;AAChH,cAAI,YAAY,SAAS,SAAS,QAC9B,gBAAgB,aAAa,CAAC,WAAW,UAAU,cAAc,IAAI,UACrE,WAAW,oBAAoB,KAAK,gBAAgB,UAAUA,UAAS,IAAI,UAAU,OAAQ,GAC7F,UAAUX,MAAK,MAAM,MAAM,CAAC,UAAU,IAAI,UAAU,KAAK,WAAW,CAAC,IAAI,WAAW,KAAK,GACzF,aAAa,YAAY,KAAK,YAAY,QAAQ,IAAI,UACtD,UACA,WACA,QAAQ,MACR,UAAU,MAAM,SAChB,eAAe,MAAM,aACrB,cAAc,MAAM;AACxB,qBAAW,SAAS,YAAY,IAAI;AACpC,oBAAU,QAAQ,MAAM,WAAW;AAEnC,sBAAY,KAAK,IAAI,GAAG,KAAK,MAAM,QAAQ,WAAW,MAAM,CAAC;AAE7D,cAAI,UAAU,OAAO,UAAU,SAAS,cAAc,QAAQ;AAC5D,gBAAI,SAAS,CAAC,MAAM,YAAY,MAAM,QAAQ,KAAK,YAAY,MAAM,GAAG;AAEtE;AAAA,YACF;AAEA,gBAAI,KAAK,YAAY,OAAO;AAC1B,wBAAU,WAAW;AAAA,YACvB;AAEA,oBAAQ,WAAW;AAAA,cACjB,UAAU,aAAa,KAAK,KAAK,IAAI,KAAK,aAAa,aAAa,GAAG,KAAK,WAAW,aAAa,CAAC,IAAI,QAAQ,WAAW,QAAQ,CAAC,CAAC;AAAA,cACtI,MAAM,KAAK,QAAQ;AAAA,cACnB,MAAM,KAAK,YAAY,MAAM;AAAA;AAAA,cAE7B,aAAa,SAAS,cAAc;AAClC,uBAAO,gBAAgB,QAAQ,IAAI,KAAK,gBAAgB,aAAa,IAAI;AAAA,cAC3E;AAAA,cACA,YAAY,SAAS,aAAa;AAChC,qBAAK,OAAO;AACZ,2BAAW,WAAW;AAEtB,oBAAI,aAAa,CAAC,UAAU;AAE1B,+BAAa,WAAW,QAAQ,iBAAiB,UAAU,UAAU,SAAS,UAAU,KAAK,IAAI,UAAU,SAAS,QAAQ;AAAA,gBAC9H;AAEA,wBAAQ,QAAQ,aAAa,CAAC,WAAW,UAAU,cAAc,IAAI,KAAK;AAC1E,kCAAkB,eAAe,IAAI;AACrC,+BAAe,YAAY,IAAI;AAAA,cACjC;AAAA,YACF,GAAG,QAAQ,UAAU,QAAQ,YAAY,SAAS,UAAU,MAAM;AAClE,uBAAW,QAAQ,MAAM,QAAQ,KAAK;AAAA,UACxC;AAAA,QACF,WAAW,KAAK,YAAY,aAAa,QAAQ;AAC/C,0BAAgB,QAAQ,IAAI;AAAA,QAC9B;AAAA,MACF,CAAC,EAAE,MAAM;AAAA,IACX;AAEA,WAAO,KAAK,EAAE,IAAI;AAClB,cAAU,KAAK,UAAU,WAAW,WAAW,QAAQ,QAAQ,GAAG;AAElE,yBAAqB,WAAW,QAAQ,SAAS,QAAQ,MAAM;AAC/D,2BAAuB,qBAAqB,mBAAmB,IAAI;AACnE,UAAM,QAAQ,OAAO,UAAU,WAAW,GAAG;AAC7C,cAAU,WAAW,MAAM,cAAc;AAAA,MACvC,SAAS;AAAA,MACT,WAAW;AAAA,IACb;AAEA,QAAI,KAAK;AACP,qBAAe,SAAS,eAAe,YAAY,aAAa,CAAC,cAAc,IAAI,cAAc,IAAI,WAAW,SAAS,kBAAkB,IAAI,UAAU,EAAE,YAAY,SAAS,QAAQ;AAExL,WAAK,MAAM;AACX,iBAAWA,MAAK,KAAK,SAAS,GAAG;AAEjC,UAAI,CAAC,SAAS,QAAQ;AAEpB,YAAI,WAAW;AACb,sBAAY,WAAW,SAAS;AAChC,uBAAa,CAAC,UAAU,aAAa,YAAY,UAAU,WAAW,UAAU;AAEhF,mBAAS,iBAAiB,CAAC,CAAC;AAC5B,wBAAc,SAAS,cAAc,UAAU,SAAS;AAAA,QAC1D;AAEA,iBAAS,SAAS,SAAS,aAAaG,MAAK,cAAc,KAAK;AAChE,eAAO,UAAU,IAAI,YAAY;AACjC,cAAM,OAAO,UAAU,IAAI,gBAAgB,EAAE;AAC7C,iBAAS,WAAW,mBAAmB,UAAU,GAAG;AAAA,MACtD,OAAO;AACL,2BAAmB,SAAS;AAAA,MAC9B;AAEA,WAAK,YAAY,SAASH,MAAK,IAAI,KAAK;AAAA,QACtC,SAAS;AAAA,MACX,CAAC;AACD,WAAK,SAAS,SAAS,SAAS;AAChC,WAAK,kBAAkB,GAAG;AAC1B,qBAAe,GAAG,aAAa,UAAU,GAAG;AAC5C,kBAAYA,MAAK,YAAY,GAAG;AAChC,kBAAYA,MAAK,YAAY,KAAK,UAAU,GAAG,GAAG;AAElD,iBAAW,KAAK,QAAQ,EAAE;AAE1B,iBAAW,UAAU,GAAG;AAAA,IAC1B;AAEA,QAAI,SAAS;AACX,mBAAa,UAAU,OAAO,IAAI,aAAa,SAAS,eAAe,IAAI;AAC3E,2BAAqB,cAAc,kBAAkB,IAAI,UAAU,WAAW,YAAY,CAAC;AAC3F,yBAAmB,cAAc,gBAAgB,IAAI,UAAU,WAAW,YAAY,GAAG,kBAAkB;AAC3G,eAAS,mBAAmB,WAAW,UAAU,GAAG,EAAE;AAEtD,UAAI,UAAU,WAAW,cAAc,UAAU,SAAS,KAAK,QAAQ;AAEvE,oBAAc,KAAK,cAAc,cAAc,SAAS,IAAI,SAAS,WAAW,YAAY,QAAQ,GAAG,kBAAkB;AACzH,kBAAY,KAAK,YAAY,cAAc,OAAO,IAAI,SAAS,WAAW,YAAY,QAAQ,GAAG,kBAAkB;AACnH,6BAAuB,iBAAiBA,MAAK,YAAY,CAAC,aAAa,SAAS,GAAG,UAAU,GAAG,GAAG;AAEnG,UAAI,CAAC,oBAAoB,EAAE,SAAS,UAAU,cAAc,UAAU,cAAc,MAAM,OAAO;AAC/F,0BAAkB,aAAaK,SAAQ,QAAQ;AAE/C,QAAAL,MAAK,IAAI,CAAC,oBAAoB,gBAAgB,GAAG;AAAA,UAC/C,SAAS;AAAA,QACX,CAAC;AACD,4BAAoBA,MAAK,YAAY,oBAAoB,UAAU,GAAG,GAAG;AACzE,0BAAkBA,MAAK,YAAY,kBAAkB,UAAU,GAAG,GAAG;AAAA,MACvE;AAAA,IACF;AAEA,QAAI,oBAAoB;AACtB,UAAI,cAAc,mBAAmB,KAAK,UACtC,YAAY,mBAAmB,KAAK;AACxC,yBAAmB,cAAc,YAAY,WAAY;AACvD,aAAK,OAAO,GAAG,GAAG,CAAC;AACnB,uBAAe,YAAY,MAAM,oBAAoB,aAAa,CAAC,CAAC;AAAA,MACtE,CAAC;AAAA,IACH;AAEA,SAAK,WAAW,WAAY;AAC1B,aAAO,UAAU,UAAU,QAAQ,IAAI,IAAI,CAAC;AAAA,IAC9C;AAEA,SAAK,OAAO,WAAY;AACtB,aAAO,UAAU,UAAU,QAAQ,IAAI,IAAI,CAAC;AAAA,IAC9C;AAEA,SAAK,SAAS,SAAU,QAAQ,MAAM;AACpC,UAAI,CAAC,MAAM;AACT,eAAO,KAAK,KAAK,IAAI;AAAA,MACvB;AAGA,UAAI,IAAI,WAAW,SAAS,CAAC,KAAK,SAC9B,iBAAiB;AAErB,UAAI,MAAM,KAAK,YAAY;AACzB,YAAI,GAAG;AACL,uBAAa,KAAK,IAAI,WAAW,GAAG,KAAK,OAAO,OAAO,CAAC;AAExD,yBAAe,KAAK;AACpB,6BAAmB,aAAa,UAAU,SAAS;AAAA,QACrD;AAEA,uBAAe,CAAC,aAAa,WAAW,oBAAoB,gBAAgB,EAAE,QAAQ,SAAU,GAAG;AACjG,iBAAO,EAAE,MAAM,UAAU,IAAI,SAAS;AAAA,QACxC,CAAC;AAED,YAAI,GAAG;AACL,wBAAc;AACd,eAAK,OAAO,CAAC;AAAA,QACf;AAEA,YAAI,QAAQ,CAAC,eAAe,CAAC,KAAK,WAAW;AAC3C,cAAI,GAAG;AACL,wBAAY,KAAK,QAAQ,gBAAgB;AAAA,UAC3C,OAAO;AACL,uBAAW,KAAK,QAAQ,kBAAkB,GAAG,GAAG,WAAW;AAAA,UAC7D;AAAA,QACF;AAEA,aAAK,KAAK,OAAO,CAAC;AAElB,sBAAc;AAEd,aAAK,aAAa;AAAA,MACpB;AAAA,IACF;AAEA,SAAK,UAAU,SAAU,MAAM,OAAO,UAAU,WAAW;AAEzD,WAAK,eAAe,CAAC,KAAK,YAAY,CAAC,OAAO;AAC5C;AAAA,MACF;AAEA,UAAI,OAAO,QAAQ,iBAAiB;AAClC,QAAA6C,cAAaW,gBAAe,aAAa,YAAY;AAErD;AAAA,MACF;AAEA,OAAC,kBAAkB,iBAAiB,cAAc,IAAI;AACtD,oBAAc;AAEd,UAAI,QAAQ,SAAS,CAAC,UAAU;AAE9B,gBAAQ,MAAM,KAAK;AACnB,gBAAQ,QAAQ;AAAA,MAClB;AAEA,oBAAc,WAAW,MAAM;AAE/B,UAAI,uBAAuB,WAAW;AACpC,kBAAU,OAAO;AAAA,UACf,MAAM;AAAA,QACR,CAAC,EAAE,WAAW;AACd,kBAAU,eAAe,UAAU,YAAY,MAAM,MAAM,KAAK,EAAE,QAAQ,SAAU,GAAG;AACrF,iBAAO,EAAE,KAAK,mBAAmB,EAAE,OAAO,GAAG,MAAM,IAAI;AAAA,QACzD,CAAC;AAAA,MACH;AAEA,WAAK,cAAc,KAAK,OAAO,MAAM,IAAI;AACzC,WAAK,gBAAgB;AAErB,UAAI,OAAO,gBAAgB,GACvB,iBAAiB,mBAAmB,GACpC,MAAM,qBAAqB,mBAAmB,SAAS,IAAI,WAAW,UAAU,SAAS,GACzF,iBAAiB,UAAU,QAAQ,CAAC,QACpCqB,UAAS,GACT,iBAAiB,aAAa,GAC9B,YAAY,UAAU,QAAQ,IAAI,SAAS,MAAM,KAAK,KACtD,mBAAmB,KAAK,cAAc,SACtC,cAAc,UAAU,QAAQ,IAAI,SAAS,QAAQ,KAAK,UAAU,KAAK,UAAU,KAAK,CAAC,UAAU,IAAI,MAAM,QAAQ,WACrH,kBAAkB,KAAK,kBAAkB,KAAK,mBAAmB,WAAW,KAAK,iBAAiB,IAAI,GACtG,eAAe,WAAW,KAAK,IAAI,GAAG,UAAU,QAAQ,IAAI,CAAC,KAAK,GAClE,IAAI,cACJC,KACA,QACA,QACA,YACA,UACA,YACA,QACA,gBACA,SACA,cACA,gBACA,mBACA;AAEJ,UAAI,WAAW,UAAU,QAAQ,GAAG;AAElC,4BAAoB9E,MAAK,YAAY,oBAAoB,UAAU,CAAC;AACpE,0BAAkBA,MAAK,YAAY,kBAAkB,UAAU,CAAC;AAAA,MAClE;AAEA,aAAO,MAAM,GAAG;AAEd,qBAAa,UAAU,CAAC;AACxB,mBAAW,OAAO,WAAW,QAAQ,GAAG,CAAC,MAAM,cAAc;AAE7D,iBAAS,WAAW;AAEpB,YAAI,WAAW,WAAW,WAAW,WAAW,OAAO,WAAW,oBAAoB,CAAC,WAAW,YAAY;AAC5G,2BAAiB,eAAe,CAAC;AACjC,uBAAa,QAAQ,UAAU;AAE/B,qBAAW,OAAO,MAAM,IAAI;AAAA,QAC9B;AAEA,YAAI,eAAe,UAAU,CAAC,GAAG;AAE/B;AACA;AAAA,QACF;AAAA,MACF;AAEA,kBAAY,WAAW,MAAM,cAAc,YAAY,IAAI;AAC3D,oBAAc,YAAY,aAAa,SAAS,IAAI;AACpD,cAAQ,eAAe,aAAa,SAAS,MAAM,WAAW,WAAW,GAAG,aAAa,oBAAoB,MAAM,gBAAgB,aAAa,kBAAkB,KAAK,oBAAoB,KAAK,eAAe,aAAa,MAAM,MAAM,QAAS;AACjP,kBAAY,SAAS,MAAM,YAAY,UAAU,IAAI;AAErD,UAAI,UAAU,SAAS,KAAK,CAAC,UAAU,QAAQ,IAAI,GAAG;AACpD,YAAI,CAAC,UAAU,QAAQ,GAAG,GAAG;AAC3B,uBAAa,UAAU,WAAW,IAAI,YAAY,MAAM,GAAG,EAAE,CAAC,IAAI,MAAM;AAAA,QAC1E,OAAO;AACL,UAAA6E,UAAS,YAAY,UAAU,OAAO,CAAC,GAAG,IAAI;AAC9C,sBAAY,UAAU,WAAW,IAAI,eAAe,qBAAqB7E,MAAK,MAAM,SAAS,GAAG,mBAAmB,SAAS,GAAG,mBAAmB,cAAc,OAAO,mBAAmB,cAAc,KAAK,KAAK,IAAI,SAAS6E;AAE/N,6BAAmB;AAAA,QACrB;AAAA,MACF;AAEA,kBAAY,YAAY,WAAW,OAAO,IAAI;AAC9C,YAAM,KAAK,IAAI,OAAO,eAAe,cAAc,mBAAmB,WAAW,MAAM,kBAAkB,MAAM,WAAW,WAAW,IAAIA,SAAQ,WAAW,kBAAkB,MAAM,gBAAgB,aAAa,kBAAkB,KAAK,oBAAoB,KAAK,aAAa,WAAW,CAAC,KAAK;AAC/R,MAAAA,UAAS;AACT,UAAI;AAEJ,aAAO,KAAK;AACV,qBAAa,UAAU,CAAC;AACxB,iBAAS,WAAW;AAEpB,YAAI,UAAU,WAAW,QAAQ,WAAW,YAAY,SAAS,CAAC,sBAAsB,WAAW,MAAM,GAAG;AAC1G,UAAAC,MAAK,WAAW,OAAO,KAAK,cAAc,KAAK,IAAI,GAAG,WAAW,KAAK,IAAI,WAAW;AAErF,eAAK,WAAW,WAAW,WAAW,QAAQ,WAAW,WAAW,SAAS,WAAW,oBAAoB,MAAM,WAAW,GAAG;AAE9H,YAAAD,WAAUC,OAAM,IAAI,WAAW;AAAA,UACjC;AAEA,qBAAW,QAAQ,kBAAkBA;AAAA,QACvC;AAAA,MACF;AAEA,eAASD;AACT,aAAOA;AACP,WAAK,gBAAgB,KAAK,eAAeA;AAEzC,UAAI,KAAK,aAAa,CAAC,gBAAgB;AACrC,aAAK,YAAY,OAAO;AACxB,cAAM,KAAK,IAAI,KAAK,WAAW,UAAU,SAAS,CAAC;AAAA,MACrD;AAEA,eAAS,MAAM,UAAU,SAAS,SAAS;AAE3C,UAAI,gBAAgB;AAElB,uBAAe7E,MAAK,MAAM,MAAM,GAAG,GAAGA,MAAK,MAAM,UAAU,OAAO,KAAK,UAAU,CAAC;AAAA,MACpF;AAEA,WAAK,WAAW;AAEhB,UAAI,eAAe6E,SAAQ;AAEzB,QAAAC,MAAK,CAAC;AACN,QAAAA,IAAG,UAAU,CAAC,IAAI,OAAOD;AACzB,4BAAoBC,IAAG,UAAU,CAAC,IAAI,OAAO,WAAW;AACxD,QAAA9E,MAAK,IAAI,CAAC,aAAa,SAAS,GAAG8E,GAAE;AAAA,MACvC;AAEA,UAAI,OAAO,EAAE,gBAAgB,KAAK,OAAO,WAAW,UAAU,SAAS,IAAI;AACzE,QAAAA,MAAK,kBAAkB,GAAG;AAC1B,qBAAa,cAAc;AAC3B,iBAAS,WAAW;AAEpB,mBAAW,WAAW,UAAU,UAAU,CAAC,CAAC,IAAI;AAEhD,YAAI,CAAC,OAAO,MAAM,GAAG;AAEnB,4BAAkB,aAAa3E,MAAK,oBAAoBC,UAAS,UAAU;AAC3E,2BAAiB;AAAA,YACf,OAAO;AAAA,YACP,OAAO,eAAe,aAAa,UAAU,EAAE,YAAY,CAAC;AAAA,UAC9D;AAEA,cAAI,cAAc,kBAAkBC,MAAK,EAAE,aAAa,UAAU,EAAE,YAAY,CAAC,MAAM,UAAU;AAE/F,2BAAe,MAAM,aAAa,UAAU,EAAE,YAAY,CAAC,IAAI;AAAA,UACjE;AAAA,QACF;AAEA,mBAAW,KAAK,QAAQyE,GAAE;AAE1B,mBAAW,UAAU,GAAG;AAExB,iBAAS,WAAW,KAAK,IAAI;AAC7B,yBAAiB,oBAAoB,eAAe,UAAU,aAAa,cAAc,SAAS,EAAE;AAEpG,YAAI,YAAY;AACd,wBAAc,CAAC,aAAa,UAAU,KAAK,SAAS,iBAAiB,GAAG;AACxE,sBAAY,IAAI;AAChB,cAAI,eAAe,WAAW,SAAS,KAAK,SAAS,IAAI,SAAS,iBAAiB;AAEnF,cAAI,GAAG;AACL,wBAAY,KAAK,UAAU,GAAG,IAAI,GAAG;AAErC,mBAAO,MAAM,cAAc,WAAW,OAAO,MAAM,YAAY,IAAI;AAAA,UACrE;AAEA,oBAAU,WAAW;AAErB,cAAI,iBAAiB;AAEnB,sBAAU,QAAQ,SAAU,GAAG;AAC7B,kBAAI,EAAE,QAAQ,mBAAmB,EAAE,KAAK,eAAe,OAAO;AAC5D,kBAAE,gBAAgB;AAAA,cACpB;AAAA,YACF,CAAC;AAAA,UACH;AAEA,8BAAoB,WAAW,UAAU;AAAA,QAC3C,OAAO;AACL,cAAI,SAAS,KAAK,SAAS;AAC3B,eAAK,OAAO,MAAM,cAAc,WAAW,OAAO,MAAM,YAAY,IAAI;AAAA,QAC1E;AAEA,YAAI,kBAAkB;AACpB,qBAAW;AAAA,YACT,KAAK,OAAO,OAAO,aAAa,SAAS,QAAQ,kBAAkB;AAAA,YACnE,MAAM,OAAO,QAAQ,aAAa,iBAAiB,SAAS,SAAS;AAAA,YACrE,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AACA,mBAAS,MAAM,IAAI,SAAS,QAAQ,MAAM,IAAI,KAAK,KAAK,OAAO,KAAK,IAAI;AACxE,mBAAS,OAAO,IAAI,SAAS,QAAQ,OAAO,IAAI,KAAK,KAAK,OAAO,MAAM,IAAI;AAC3E,mBAAS,OAAO,IAAI,SAAS,UAAU,IAAI,IAAI,SAAS,UAAU,MAAM,IAAI,SAAS,UAAU,OAAO,IAAI,SAAS,UAAU,KAAK,IAAI;AACtI,mBAAS,QAAQ,IAAIA,IAAG,QAAQ;AAChC,mBAAS,WAAW,IAAI,IAAIA,IAAG,WAAW,IAAI;AAC9C,mBAAS,WAAW,MAAM,IAAIA,IAAG,WAAW,MAAM;AAClD,mBAAS,WAAW,OAAO,IAAIA,IAAG,WAAW,OAAO;AACpD,mBAAS,WAAW,KAAK,IAAIA,IAAG,WAAW,KAAK;AAChD,2BAAiB,WAAW,kBAAkB,UAAU,WAAW;AACnE,4BAAkB,WAAW,CAAC;AAAA,QAChC;AAEA,YAAI,WAAW;AAEb,oBAAU,UAAU;AAEpB,8BAAoB,CAAC;AAErB,oBAAU,OAAO,UAAU,SAAS,GAAG,MAAM,IAAI;AACjD,sBAAY,UAAU,UAAU,CAAC,IAAI,WAAW,SAAS;AACzD,qBAAW,KAAK,IAAI,SAAS,SAAS,IAAI;AAC1C,8BAAoB,YAAY,eAAe,OAAO,eAAe,SAAS,GAAG,CAAC;AAElF,oBAAU,OAAO,GAAG,MAAM,IAAI;AAC9B,qBAAW,UAAU,WAAW,IAAI;AACpC,oBAAU,UAAU,UAAU,UAAU,UAAU,UAAU,CAAC;AAE7D,8BAAoB,CAAC;AAAA,QACvB,OAAO;AACL,sBAAY;AAAA,QACd;AAEA,2BAAmB,eAAe,QAAQ,eAAe,MAAM,aAAa,UAAU,EAAE,YAAY,CAAC,IAAI,eAAe,QAAQ,eAAe,MAAM,eAAe,cAAc,UAAU,CAAC;AAAA,MAC/L,WAAW,WAAW,WAAW,KAAK,CAAC,oBAAoB;AAEzD,iBAAS,QAAQ;AAEjB,eAAO,UAAU,WAAWzE,QAAO;AACjC,cAAI,OAAO,YAAY;AACrB,qBAAS,OAAO;AAChB,mBAAO,OAAO;AAAA,UAChB;AAEA,mBAAS,OAAO;AAAA,QAClB;AAAA,MACF;AAEA,sBAAgB,aAAa,QAAQ,SAAU,GAAG;AAChD,eAAO,EAAE,OAAO,OAAO,IAAI;AAAA,MAC7B,CAAC;AACD,WAAK,QAAQ;AACb,WAAK,MAAM;AACX,gBAAU,UAAU,iBAAiB,aAAa,WAAW;AAE7D,UAAI,CAAC,sBAAsB,CAAC,gBAAgB;AAC1C,kBAAU,cAAc,WAAW,UAAU;AAC7C,aAAK,OAAO,MAAM;AAAA,MACpB;AAEA,WAAK,OAAO,OAAO,IAAI;AACvB,oBAAcM,UAAS;AAEvB,UAAI,iBAAiB;AACnB,mBAAW;AAGX,wBAAgB,QAAQ,IAAI;AAAA,MAC9B;AAEA,oBAAc;AACd,mBAAa,aAAa,UAAU,YAAY,qBAAqB,UAAU,SAAS,MAAM,oBAAoB,UAAU,SAAS,oBAAoB,GAAG,IAAI,EAAE,OAAO,UAAU,KAAK,GAAG,MAAM,IAAI;AAErM,UAAI,kBAAkB,iBAAiB,KAAK,YAAY,sBAAsB,uBAAuB,aAAa,CAAC,UAAU,UAAU;AAErI,qBAAa,CAAC,aAAa,UAAU,YAAY,gBAAgB,UAAU,KAAK,oBAAoB,UAAU,UAAU,cAAc,sBAAsB,QAAQ,SAAU,CAAC,eAAeX,MAAK,MAAM,UAAU,OAAO,KAAK,CAAC,IAAI,cAAc,IAAI;AAEtP,aAAK,WAAW,mBAAmB,UAAU,SAAS,WAAW,eAAe,IAAI;AAAA,MACtF;AAEA,aAAO,eAAe,OAAO,aAAa,KAAK,MAAM,KAAK,WAAW,SAAS;AAC9E,oBAAc,WAAW,WAAW;AAEpC,UAAI,CAAC,MAAM,iBAAiB,GAAG;AAE7B,6BAAqBA,MAAK,YAAY,oBAAoB,UAAU,CAAC;AACrE,2BAAmBA,MAAK,YAAY,kBAAkB,UAAU,CAAC;AAEjE,qBAAa,oBAAoB,WAAW,iBAAiB;AAE7D,qBAAa,aAAa,WAAW,qBAAqB,aAAa,EAAE;AAEzE,qBAAa,kBAAkB,WAAW,eAAe;AAEzD,qBAAa,WAAW,WAAW,mBAAmB,aAAa,EAAE;AAAA,MACvE;AAEA,wBAAkB,CAAC,kBAAkB,KAAK,OAAO;AAEjD,UAAI,aAAa,CAAC,kBAAkB,CAAC,oBAAoB;AAEvD,6BAAqB;AACrB,kBAAU,IAAI;AACd,6BAAqB;AAAA,MACvB;AAAA,IACF;AAEA,SAAK,cAAc,WAAY;AAC7B,cAAQ,WAAW,IAAI,YAAYW,UAAS,IAAI,UAAU,OAAQ;AAAA,IACpE;AAEA,SAAK,eAAe,WAAY;AAC9B,oBAAc,KAAK,iBAAiB;AAEpC,UAAI,WAAW;AACb,qBAAa,WAAW,SAAS,CAAC,IAAI,CAAC,UAAU,OAAO,IAAI,cAAc,WAAW,UAAU,SAAS,CAAC,IAAI,YAAY,cAAc,WAAW,KAAK,YAAY,GAAG,CAAC;AAAA,MACzK;AAAA,IACF;AAEA,SAAK,gBAAgB,SAAU,OAAO;AACpC,aAAO,aAAa,UAAU,WAAW,SAAS,KAAK,QAAQ,KAAK,SAAS,UAAU,OAAO,KAAK,IAAI,UAAU,SAAS,IAAI,UAAU;AAAA,IAC1I;AAEA,SAAK,cAAc,SAAU,MAAM;AACjC,UAAI,IAAI,UAAU,QAAQ,IAAI,GAC1B,IAAI,KAAK,YAAY,IAAI,UAAU,MAAM,GAAG,CAAC,EAAE,QAAQ,IAAI,UAAU,MAAM,IAAI,CAAC;AAEpF,cAAQ,UAAU,IAAI,IAAI,EAAE,OAAO,SAAU,GAAG;AAC9C,eAAO,EAAE,KAAK,oBAAoB;AAAA,MACpC,CAAC,IAAI,GAAG,OAAO,SAAU,GAAG;AAC1B,eAAO,KAAK,YAAY,IAAI,EAAE,OAAO,QAAQ,EAAE,SAAS;AAAA,MAC1D,CAAC;AAAA,IACH;AAEA,SAAK,SAAS,SAAU,OAAO,gBAAgB,WAAW;AACxD,UAAI,sBAAsB,CAAC,aAAa,CAAC,OAAO;AAC9C;AAAA,MACF;AAEA,UAAI,SAAS,mBAAmB,OAAO,aAAa,KAAK,OAAO,GAC5D,IAAI,QAAQ,KAAK,SAAS,SAAS,QACnC,UAAU,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,GACvCoE,gBAAe,KAAK,UACpB,UACA,WACA,aACA,QACA,cACA,SACA,SACA;AAEJ,UAAI,gBAAgB;AAClB,kBAAU;AACV,kBAAU,qBAAqB,WAAW,IAAI;AAE9C,YAAI,MAAM;AACR,kBAAQ;AACR,kBAAQ,aAAa,CAAC,WAAW,UAAU,cAAc,IAAI;AAAA,QAC/D;AAAA,MACF;AAGA,UAAI,iBAAiB,OAAO,CAAC,eAAe,CAACrE,aAAY,iBAAiB;AACxE,YAAI,CAAC,WAAW,QAAQ,UAAU,SAAS,YAAYC,UAAS,IAAI,UAAU,eAAe;AAC3F,oBAAU;AAAA,QACZ,WAAW,YAAY,KAAK,MAAM,UAAU,SAAS,YAAYA,UAAS,IAAI,UAAU,eAAe;AACrG,oBAAU;AAAA,QACZ;AAAA,MACF;AAEA,UAAI,YAAYoE,iBAAgB,KAAK,SAAS;AAC5C,mBAAW,KAAK,WAAW,CAAC,CAAC,WAAW,UAAU;AAClD,oBAAY,CAAC,CAACA,iBAAgBA,gBAAe;AAC7C,kBAAU,aAAa;AACvB,uBAAe,WAAW,CAAC,CAAC,YAAY,CAAC,CAACA;AAE1C,aAAK,YAAY,UAAUA,gBAAe,IAAI;AAC9C,aAAK,WAAW;AAEhB,YAAI,gBAAgB,CAAC,aAAa;AAChC,wBAAc,WAAW,CAACA,gBAAe,IAAI,YAAY,IAAI,IAAIA,kBAAiB,IAAI,IAAI;AAE1F,cAAI,UAAU;AACZ,qBAAS,CAAC,WAAW,cAAc,cAAc,CAAC,MAAM,UAAU,cAAc,cAAc,CAAC,KAAK,cAAc,WAAW;AAE7H,6BAAiB,cAAc,WAAW,cAAc,WAAW,WAAW,UAAU;AAAA,UAC1F;AAAA,QACF;AAEA,4BAAoB,WAAW,oBAAoB,kBAAkB,SAAS,CAAC,eAAe,YAAY,eAAe,IAAI,gBAAgB,IAAI,IAAI,KAAK,YAAY,eAAe,EAAE,QAAQ,SAAU,GAAG;AAC1M,iBAAO,EAAE,aAAa;AAAA,QACxB,CAAC;AAED,YAAI,CAAC,UAAU;AACb,cAAI,cAAc,CAAC,eAAe,CAACrE,WAAU;AAC3C,uBAAW,IAAI,QAAQ,WAAW,WAAW,WAAW,SAAS,WAAW,OAAO,WAAW,IAAI,QAAQ,WAAW,MAAM;AAE3H,gBAAI,WAAW,SAAS;AACtB,yBAAW,QAAQ,iBAAiB,SAAS,UAAU,SAAS,UAAU,KAAK;AAAA,YACjF,OAAO;AAEL,yBAAW,KAAK,gBAAgB;AAChC,yBAAW,WAAW,EAAE,QAAQ;AAAA,YAClC;AAAA,UACF,WAAW,WAAW;AACpB,sBAAU,cAAc,SAAS,CAAC,EAAE,gBAAgB,eAAe,OAAO;AAAA,UAC5E;AAAA,QACF;AAEA,YAAI,KAAK;AACP,mBAAS,eAAe,OAAO,MAAM,aAAa,UAAU,GAAG,IAAI;AAEnE,cAAI,CAAC,kBAAkB;AACrB,sBAAU,OAAO,WAAW,YAAY,OAAO,CAAC;AAAA,UAClD,WAAW,cAAc;AACvB,sBAAU,CAAC,SAAS,UAAUqE,iBAAgB,MAAM,IAAI,UAAU,SAAS,KAAK,WAAW,UAAU,SAAS;AAE9G,gBAAI,aAAa;AACf,kBAAI,CAAC,UAAU,YAAY,UAAU;AACnC,oBAAI,SAAS,WAAW,KAAK,IAAI,GAC7B,UAAU,SAAS;AAEvB,0BAAU,KAAK1E,QAAO,OAAO,OAAO,cAAc,YAAY,UAAU,KAAK,KAAK,OAAO,QAAQ,cAAc,YAAY,IAAI,WAAW,GAAG;AAAA,cAC/I,OAAO;AACL,0BAAU,KAAK,MAAM;AAAA,cACvB;AAAA,YACF;AAEA,sBAAU,YAAY,UAAU,iBAAiB,QAAQ;AAEzD,wBAAY,UAAU,KAAK,YAAY,UAAU,YAAY,YAAY,KAAK,CAAC,UAAU,YAAY,EAAE;AAAA,UACzG;AAAA,QACF;AAEA,gBAAQ,CAAC,QAAQ,SAAS,CAAC,eAAe,CAACK,aAAY,gBAAgB,QAAQ,IAAI;AACnF,wBAAgB,WAAW,QAAQ,YAAY,UAAU,KAAK,CAAC,qBAAqB,SAAS,YAAY,OAAO,EAAE,QAAQ,SAAU,IAAI;AACtI,iBAAO,GAAG,UAAU,YAAY,OAAO,QAAQ,QAAQ,EAAE,YAAY,SAAS;AAAA,QAChF,CAAC;AAED,oBAAY,CAAC,YAAY,CAAC,SAAS,SAAS,IAAI;AAEhD,YAAI,gBAAgB,CAAC,aAAa;AAChC,cAAI,UAAU;AACZ,gBAAI,gBAAgB;AAClB,kBAAI,WAAW,YAAY;AACzB,0BAAU,MAAM,EAAE,cAAc,CAAC;AAAA,cACnC,WAAW,WAAW,SAAS;AAC7B,0BAAU,QAAQ,IAAI,EAAE,MAAM;AAAA,cAChC,WAAW,WAAW,WAAW;AAC/B,0BAAU,QAAQ,IAAI;AAAA,cACxB,OAAO;AACL,0BAAU,MAAM,EAAE;AAAA,cACpB;AAAA,YACF;AAEA,wBAAY,SAAS,IAAI;AAAA,UAC3B;AAEA,cAAI,WAAW,CAAC,iBAAiB;AAE/B,wBAAY,WAAW,UAAU,MAAM,QAAQ;AAC/C,sBAAU,WAAW,KAAK,UAAU,MAAM,UAAU,WAAW,CAAC;AAChE,qBAAS,YAAY,IAAI,KAAK,KAAK,OAAO,CAAC,IAAI,UAAU,WAAW,IAAI;AAExE,gBAAI,CAAC,SAAS;AAEZ,4BAAc,YAAY,IAAI,IAAI;AAClC,wBAAU,WAAW,KAAK,UAAU,MAAM,UAAU,WAAW,CAAC;AAAA,YAClE;AAAA,UACF;AAEA,cAAI,iBAAiB,CAAC,YAAY,KAAK,IAAI,KAAK,YAAY,CAAC,KAAK,UAAU,aAAa,IAAI,gBAAgB,OAAO;AAClH,0BAAc,KAAK,iBAAiB;AAEpC,yBAAa,WAAW,SAAS,CAAC,IAAI,cAAc,WAAW,WAAW,YAAY,IAAI,CAAC,SAAS,CAAC;AAAA,UACvG;AAAA,QACF,WAAW,YAAY,YAAY,CAAC,aAAa;AAC/C,mBAAS,IAAI;AAAA,QACf;AAAA,MACF;AAGA,UAAI,iBAAiB;AACnB,YAAI,IAAI,qBAAqB,SAAS,mBAAmB,SAAS,KAAK,mBAAmB,iBAAiB,KAAK;AAChH,0BAAkB,KAAK,mBAAmB,aAAa,IAAI,EAAE;AAC7D,wBAAgB,CAAC;AAAA,MACnB;AAEA,wBAAkB,eAAe,CAAC,SAAS,mBAAmB,SAAS,KAAK,mBAAmB,iBAAiB,EAAE;AAAA,IACpH;AAEA,SAAK,SAAS,SAAU,OAAO,SAAS;AACtC,UAAI,CAAC,KAAK,SAAS;AACjB,aAAK,UAAU;AAEf,QAAAmC,cAAa,UAAU,UAAU,SAAS;AAE1C,sBAAcA,cAAa,UAAU,UAAUO,UAAS;AACxD,yBAAiBP,cAAaW,gBAAe,eAAe,aAAa;AAEzE,YAAI,UAAU,OAAO;AACnB,eAAK,WAAW,eAAe;AAC/B,oBAAU,UAAU,WAAW,WAAW;AAAA,QAC5C;AAEA,oBAAY,SAAS,KAAK,QAAQ;AAAA,MACpC;AAAA,IACF;AAEA,SAAK,WAAW,SAAUwB,OAAM;AAC9B,aAAOA,SAAQ,UAAU,QAAQ,QAAQ;AAAA,IAC3C;AAEA,SAAK,eAAe,SAAU,UAAU,QAAQ,WAAW,WAAW;AAEpE,UAAI,oBAAoB;AAEtB,YAAI,KAAK,mBAAmB,eACxB,WAAW,mBAAmB,SAAS,GACvC,UAAU,GAAG,MAAM,GAAG;AAE1B,mBAAW,GAAG,QAAQ,UAAU,WAAW;AAC3C,iBAAS,GAAG,QAAQ,UAAU,SAAS;AAAA,MACzC;AAEA,WAAK,QAAQ,OAAO,OAAO;AAAA,QACzB,OAAO,WAAW,UAAU,aAAa,CAAC,CAAC,KAAK,WAAW;AAAA,QAC3D,KAAK,WAAW,QAAQ,aAAa,CAAC,CAAC,KAAK,SAAS;AAAA,MACvD,GAAG,SAAS;AACZ,WAAK,OAAO;AAAA,IACd;AAEA,SAAK,mBAAmB,SAAU,QAAQ;AACxC,UAAI,eAAe,QAAQ;AACzB,YAAI,IAAI,YAAY,QAAQ,UAAU,CAAC,IAAI;AAC3C,oBAAY,CAAC,IAAI,WAAW,YAAY,CAAC,CAAC,IAAI,SAAS;AACvD,oBAAY,CAAC,IAAI,WAAW,YAAY,CAAC,CAAC,IAAI,SAAS;AAEvD,kBAAU,WAAW;AAAA,MACvB;AAAA,IACF;AAEA,SAAK,UAAU,SAAU,OAAO,gBAAgB;AAC9C,UAAI,KAAK,SAAS;AAChB,kBAAU,SAAS,KAAK,OAAO,MAAM,IAAI;AACzC,aAAK,UAAU,KAAK,WAAW;AAC/B,0BAAkB,cAAc,WAAW,MAAM;AACjD,qBAAa;AACb,qBAAa,SAAS,UAAU;AAChC,yBAAiBlC,iBAAgBU,gBAAe,eAAe,aAAa;AAE5E,YAAI,iBAAiB;AACnB,0BAAgB,MAAM;AACtB,kBAAQ,SAAS,QAAQ,MAAM,KAAK,MAAM,QAAQ,QAAQ;AAAA,QAC5D;AAEA,YAAI,CAAC,YAAY;AACf,cAAI,IAAI,UAAU;AAElB,iBAAO,KAAK;AACV,gBAAI,UAAU,CAAC,EAAE,aAAa,YAAY,UAAU,CAAC,MAAM,MAAM;AAC/D;AAAA,YACF;AAAA,UACF;AAEA,UAAAV,iBAAgB,UAAU,UAAU,SAAS;AAE7C,wBAAcA,iBAAgB,UAAU,UAAUM,UAAS;AAAA,QAC7D;AAAA,MACF;AAAA,IACF;AAEA,SAAK,OAAO,SAAU,QAAQ,gBAAgB;AAC5C,WAAK,QAAQ,QAAQ,cAAc;AACnC,oBAAc,CAAC,kBAAkB,WAAW,KAAK;AACjD,YAAM,OAAO,KAAK,EAAE;AAEpB,UAAI,IAAI,UAAU,QAAQ,IAAI;AAE9B,WAAK,KAAK,UAAU,OAAO,GAAG,CAAC;AAC/B,YAAM,MAAM,aAAa,KAAK;AAG9B,UAAI;AAEJ,gBAAU,QAAQ,SAAU,GAAG;AAC7B,eAAO,EAAE,aAAa,KAAK,aAAa,IAAI;AAAA,MAC9C,CAAC;AAED,WAAK,mBAAmB,KAAK,OAAO,MAAM;AAE1C,UAAI,WAAW;AACb,kBAAU,gBAAgB;AAC1B,kBAAU,UAAU,OAAO;AAAA,UACzB,MAAM;AAAA,QACR,CAAC;AACD,0BAAkB,UAAU,KAAK;AAAA,MACnC;AAEA,qBAAe,CAAC,aAAa,WAAW,oBAAoB,gBAAgB,EAAE,QAAQ,SAAU,GAAG;AACjG,eAAO,EAAE,cAAc,EAAE,WAAW,YAAY,CAAC;AAAA,MACnD,CAAC;AACD,mBAAa,SAAS,WAAW;AAEjC,UAAI,KAAK;AACP,qBAAa,SAAS,UAAU;AAChC,YAAI;AAEJ,kBAAU,QAAQ,SAAU,GAAG;AAC7B,iBAAO,EAAE,QAAQ,OAAO;AAAA,QAC1B,CAAC;AAED,cAAM,SAAS,SAAS;AAAA,MAC1B;AAEA,WAAK,UAAU,KAAK,OAAO,IAAI;AAAA,IACjC;AAEA,cAAU,KAAK,IAAI;AAEnB,SAAK,OAAO,OAAO,KAAK;AACxB,0BAAsB,mBAAmB,IAAI;AAE7C,QAAI,aAAa,UAAU,OAAO,CAAC,QAAQ;AAEzC,UAAI,aAAa,KAAK;AAEtB,WAAK,SAAS,WAAY;AACxB,aAAK,SAAS;AACd,mBAAW;AAEX,iBAAS,OAAO,KAAK,QAAQ;AAAA,MAC/B;AAEA,MAAApD,MAAK,YAAY,MAAM,KAAK,MAAM;AAClC,eAAS;AACT,cAAQ,MAAM;AAAA,IAChB,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAEA,WAAO,iBAAiB;AAAA,EAC1B;AAEA,EAAAwD,eAAc,WAAW,SAAS,SAAS,MAAM;AAC/C,QAAI,CAACvD,eAAc;AACjB,MAAAD,QAAO,QAAQoB,UAAS;AACxB,oBAAc,KAAK,OAAO,YAAYoC,eAAc,OAAO;AAC3D,MAAAvD,gBAAe;AAAA,IACjB;AAEA,WAAOA;AAAA,EACT;AAEA,EAAAuD,eAAc,WAAW,SAAS,SAAS,QAAQ;AACjD,QAAI,QAAQ;AACV,eAAS,KAAK,QAAQ;AACpB,kBAAU,CAAC,IAAI,OAAO,CAAC;AAAA,MACzB;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,EAAAA,eAAc,UAAU,SAAS,QAAQ,OAAO,MAAM;AACpD,eAAW;AAEX,cAAU,QAAQ,SAAU,SAAS;AACnC,aAAO,QAAQ,OAAO,SAAS,SAAS,EAAE,KAAK;AAAA,IACjD,CAAC;AAED,IAAAV,iBAAgB5C,OAAM,SAASkD,UAAS;AAExC,IAAAN,iBAAgB3C,OAAM,UAAUiD,UAAS;AAEzC,kBAAc,aAAa;AAE3B,IAAAN,iBAAgB3C,OAAM,eAAe,YAAY;AAEjD,IAAA2C,iBAAgBzC,QAAO,cAAc,YAAY;AAEjD,mBAAeyC,kBAAiB3C,OAAM,oCAAoC,mBAAmB;AAE7F,mBAAe2C,kBAAiB3C,OAAM,8BAA8B,iBAAiB;AAErF,iBAAa,KAAK;AAElB,wBAAoB2C,gBAAe;AAEnC,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,qBAAeA,kBAAiB,WAAW,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC;AAEhE,qBAAeA,kBAAiB,WAAW,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC;AAAA,IAClE;AAAA,EACF;AAEA,EAAAU,eAAc,SAAS,SAAS,SAAS;AACvC,IAAAtD,QAAO;AACP,IAAAC,QAAO;AACP,IAAAC,UAASD,MAAK;AACd,IAAAE,SAAQF,MAAK;AAEb,QAAIH,OAAM;AACR,iBAAWA,MAAK,MAAM;AACtB,MAAAO,UAASP,MAAK,MAAM;AACpB,MAAAS,YAAWT,MAAK,KAAK,WAAW;AAChC,4BAAsBA,MAAK,KAAK,sBAAsB;AACtD,2BAAqBE,MAAK,QAAQ,qBAAqB;AACvD,oBAAcA,MAAK,eAAe;AAClC,MAAAF,MAAK,KAAK,QAAQ,iBAAiBwD,cAAa;AAEhD,UAAInD,QAAO;AACT,mBAAW;AACX,oBAAY,SAAS,cAAc,KAAK;AAExC,kBAAU,MAAM,SAAS;AACzB,kBAAU,MAAM,WAAW;AAE3B,sBAAc;AAEd,mBAAW;AAEX,iBAAS,SAASL,KAAI;AAEtB,QAAAwD,eAAc,UAAU,SAAS;AACjC,qBAAa,SAAS,WAAW,0BAA0B,KAAK,UAAU,SAAS;AAEnF,8BAAsB,SAAS,YAAY;AAE3C,QAAAX,cAAa3C,OAAM,SAASkD,UAAS;AAGrC,QAAA9C,SAAQ,CAACJ,OAAMC,OAAMC,SAAQC,MAAK;AAElC,YAAIL,MAAK,YAAY;AACnB,UAAAwD,eAAc,aAAa,SAAU,MAAM;AACzC,gBAAI,KAAKxD,MAAK,WAAW,GACrB;AAEJ,iBAAK,KAAK,MAAM;AACd,iBAAG,IAAI,GAAG,KAAK,CAAC,CAAC;AAAA,YACnB;AAEA,mBAAO;AAAA,UACT;AAEA,UAAAA,MAAK,iBAAiB,kBAAkB,WAAY;AAClD,mBAAO,WAAW;AAAA,UACpB,CAAC;AACD,UAAAA,MAAK,iBAAiB,oBAAoB,WAAY;AACpD,mBAAO,gBAAgB;AAAA,UACzB,CAAC;AACD,UAAAA,MAAK,iBAAiB,cAAc,WAAY;AAC9C,wBAAY,GAAG,CAAC;AAEhB,sBAAU,YAAY;AAAA,UACxB,CAAC;AACD,UAAAA,MAAK,WAAW,EAAE,IAAI,2BAA2B,WAAY;AAE3D,+BAAmB;AAEnB,mBAAO;AAAA,UACT,CAAC;AAAA,QACH,OAAO;AACL,kBAAQ,KAAK,+BAA+B;AAAA,QAC9C;AAEA,2BAAmB;AAEnB,QAAA6C,cAAa1C,OAAM,UAAUiD,UAAS;AAGtC,YAAI,eAAe/C,OAAM,aAAa,OAAO,GACzC,YAAYA,OAAM,OAClB,SAAS,UAAU,gBACnB,iBAAiBL,MAAK,KAAK,UAAU,WACrC,QACA;AAEJ,uBAAe,UAAU,OAAO,eAAe,gBAAgB,UAAU;AAAA,UACvE,OAAO,SAAS,QAAQ;AACtB,mBAAO,KAAK,KAAK,OAAO,IAAI;AAAA,UAC9B;AAAA,QACF,CAAC;AAED,kBAAU,iBAAiB;AAE3B,iBAAS,WAAWK,MAAK;AACzB,kBAAU,IAAI,KAAK,MAAM,OAAO,MAAM,UAAU,GAAG,CAAC,KAAK;AAEzD,oBAAY,IAAI,KAAK,MAAM,OAAO,OAAO,YAAY,GAAG,CAAC,KAAK;AAC9D,iBAAS,UAAU,iBAAiB,SAAS,UAAU,eAAe,kBAAkB;AAExF,YAAI,CAAC,cAAc;AAEjB,UAAAA,OAAM,aAAa,SAAS,EAAE;AAG9B,UAAAA,OAAM,gBAAgB,OAAO;AAAA,QAC/B;AAGA,wBAAgB,YAAY,OAAO,GAAG;AACtC,QAAAL,MAAK,YAAY,KAAK,WAAY;AAChC,iBAAOU,YAAW;AAAA,QACpB,CAAC;AAED,QAAAmC,cAAa1C,OAAM,eAAe,YAAY;AAG9C,QAAA0C,cAAaxC,QAAO,cAAc,YAAY;AAG9C,uBAAewC,eAAc1C,OAAM,oCAAoC,mBAAmB;AAE1F,uBAAe0C,eAAc1C,OAAM,8BAA8B,iBAAiB;AAElF,yBAAiBH,MAAK,MAAM,YAAY,WAAW;AAEnD,oBAAY,KAAK,cAAc;AAE/B,QAAAC,gBAAeU,UAAS;AACxB,uBAAeX,MAAK,YAAY,KAAK,WAAW,EAAE,MAAM;AACxD,uBAAe,CAACG,OAAM,oBAAoB,WAAY;AACpD,cAAI,IAAID,MAAK,YACT,IAAIA,MAAK;AAEb,cAAIC,MAAK,QAAQ;AACf,yBAAa;AACb,0BAAc;AAAA,UAChB,WAAW,eAAe,KAAK,gBAAgB,GAAG;AAChD,sBAAU;AAAA,UACZ;AAAA,QACF,GAAGA,OAAM,oBAAoB,aAAaD,OAAM,QAAQ,aAAaA,OAAM,UAAU,SAAS;AAE9F,4BAAoB2C,aAAY;AAEhC,kBAAU,QAAQ,SAAU,SAAS;AACnC,iBAAO,QAAQ,OAAO,GAAG,CAAC;AAAA,QAC5B,CAAC;AAED,aAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AACzC,yBAAeC,kBAAiB,WAAW,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC;AAEhE,yBAAeA,kBAAiB,WAAW,CAAC,GAAG,WAAW,IAAI,CAAC,CAAC;AAAA,QAClE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,EAAAU,eAAc,SAAS,SAAS,OAAO,MAAM;AAC3C,wBAAoB,SAAS,kBAAkB,CAAC,CAAC,KAAK;AACtD,QAAI,KAAK,KAAK;AACd,UAAM,cAAc,aAAa,MAAM,gBAAgB,OAAO,YAAY,OAAO,EAAE;AACnF,4BAAwB,SAAS,sBAAsBA,eAAc,YAAY,KAAK,KAAK;AAE3F,QAAI,uBAAuB,MAAM;AAC/B,0BAAoBV,gBAAe,KAAK,oBAAoBD,eAAc,KAAK,qBAAqB,MAAM;AAC1G,uBAAiB,KAAK,oBAAoB,IAAI,QAAQ,QAAQ,MAAM;AAAA,IACtE;AAAA,EACF;AAEA,EAAAW,eAAc,gBAAgB,SAAS,cAAc,QAAQ,MAAM;AACjE,QAAI,IAAI,WAAW,MAAM,GACrB,IAAI,WAAW,QAAQ,CAAC,GACxB,aAAanC,aAAY,CAAC;AAE9B,QAAI,CAAC,GAAG;AACN,iBAAW,OAAO,GAAG,aAAa,IAAI,CAAC;AAAA,IACzC;AAEA,QAAI,MAAM;AACR,mBAAa,SAAS,QAAQnB,OAAM,MAAMG,QAAO,MAAMD,SAAQ,IAAI,IAAI,SAAS,QAAQ,GAAG,IAAI;AAAA,IACjG;AAAA,EACF;AAEA,EAAAoD,eAAc,kBAAkB,SAAS,gBAAgB,OAAO;AAC9D,cAAU,QAAQ,SAAU,GAAG;AAC7B,aAAO,EAAE,QAAQ,EAAE,KAAK,UAAU,SAAS,EAAE,KAAK,KAAK,MAAM,IAAI;AAAA,IACnE,CAAC;AAAA,EACH;AAEA,EAAAA,eAAc,eAAe,SAAS,aAAa,SAAS,OAAO,YAAY;AAC7E,QAAI,UAAU,UAAU,OAAO,IAAI,WAAW,OAAO,IAAI,SAAS,sBAAsB,GACpF,SAAS,OAAO,aAAa,SAAS,OAAO,IAAI,SAAS;AAC9D,WAAO,aAAa,OAAO,QAAQ,SAAS,KAAK,OAAO,OAAO,SAAStD,MAAK,aAAa,OAAO,SAAS,SAAS,KAAK,OAAO,MAAM,SAASA,MAAK;AAAA,EACrJ;AAEA,EAAAsD,eAAc,qBAAqB,SAAS,mBAAmB,SAAS,gBAAgB,YAAY;AAClG,cAAU,OAAO,MAAM,UAAU,WAAW,OAAO;AACnD,QAAI,SAAS,QAAQ,sBAAsB,GACvC,OAAO,OAAO,aAAa,SAAS,OAAO,GAC3C,SAAS,kBAAkB,OAAO,OAAO,IAAI,kBAAkB,YAAY,UAAU,cAAc,IAAI,OAAO,CAAC,eAAe,QAAQ,GAAG,IAAI,WAAW,cAAc,IAAI,OAAO,MAAM,WAAW,cAAc,KAAK;AACzN,WAAO,cAAc,OAAO,OAAO,UAAUtD,MAAK,cAAc,OAAO,MAAM,UAAUA,MAAK;AAAA,EAC9F;AAEA,EAAAsD,eAAc,UAAU,SAAS,QAAQ,gBAAgB;AACvD,cAAU,MAAM,CAAC,EAAE,QAAQ,SAAU,GAAG;AACtC,aAAO,EAAE,KAAK,OAAO,oBAAoB,EAAE,KAAK;AAAA,IAClD,CAAC;AAED,QAAI,mBAAmB,MAAM;AAC3B,UAAI,YAAY,WAAW,WAAW,CAAC;AACvC,mBAAa,CAAC;AACd,gBAAU,QAAQ,SAAU,GAAG;AAC7B,eAAO,EAAE;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAOA;AACT,EAAE;AACFA,eAAc,UAAU;AAExBA,eAAc,aAAa,SAAU,SAAS;AAC5C,SAAO,UAAU,SAAS,OAAO,EAAE,QAAQ,SAAU,QAAQ;AAE3D,QAAI,UAAU,OAAO,OAAO;AAC1B,UAAI,IAAI,aAAa,QAAQ,MAAM;AAEnC,WAAK,KAAK,aAAa,OAAO,GAAG,CAAC;AAElC,mBAAa,KAAK,QAAQ,OAAO,MAAM,SAAS,OAAO,WAAW,OAAO,aAAa,WAAW,GAAGxD,MAAK,KAAK,SAAS,MAAM,GAAGS,UAAS,CAAC;AAAA,IAC5I;AAAA,EACF,CAAC,IAAI;AACP;AAEA+C,eAAc,SAAS,SAAU,MAAM,OAAO;AAC5C,SAAO,WAAW,CAAC,MAAM,KAAK;AAChC;AAEAA,eAAc,SAAS,SAAU,MAAM,WAAW;AAChD,SAAO,IAAIA,eAAc,MAAM,SAAS;AAC1C;AAEAA,eAAc,UAAU,SAAU,MAAM;AACtC,SAAO,OAAO,UAAU,IAAI,KAAKvD,iBAAgBuD,eAAc,SAAS,MAAM,YAAY,IAAI;AAChG;AAEAA,eAAc,SAAS,SAAU,OAAO;AACtC,SAAO,EAAE,WAAW,SAAS,WAAW,UAAU,OAAO,IAAI,CAAC;AAChE;AAEAA,eAAc,oBAAoB;AAElCA,eAAc,YAAY,SAAU,SAAS,YAAY;AACvD,SAAO,WAAW,SAAS,aAAa,cAAc,SAAS;AACjE;AAEAA,eAAc,gBAAgB,SAAU,SAAS,YAAY;AAC3D,SAAO,eAAe,WAAW,OAAO,GAAG,aAAa,cAAc,SAAS;AACjF;AAEAA,eAAc,UAAU,SAAU,IAAI;AACpC,SAAO,KAAK,EAAE;AAChB;AAEAA,eAAc,SAAS,WAAY;AACjC,SAAO,UAAU,OAAO,SAAU,GAAG;AACnC,WAAO,EAAE,KAAK,OAAO;AAAA,EACvB,CAAC;AACH;AAGAA,eAAc,cAAc,WAAY;AACtC,SAAO,CAAC,CAAC;AACX;AAEAA,eAAc,kBAAkB;AAEhCA,eAAc,mBAAmB,SAAU,MAAM,UAAU;AACzD,MAAI,IAAI,WAAW,IAAI,MAAM,WAAW,IAAI,IAAI,CAAC;AACjD,GAAC,EAAE,QAAQ,QAAQ,KAAK,EAAE,KAAK,QAAQ;AACzC;AAEAA,eAAc,sBAAsB,SAAU,MAAM,UAAU;AAC5D,MAAI,IAAI,WAAW,IAAI,GACnB,IAAI,KAAK,EAAE,QAAQ,QAAQ;AAC/B,OAAK,KAAK,EAAE,OAAO,GAAG,CAAC;AACzB;AAEAA,eAAc,QAAQ,SAAU,SAAS,MAAM;AAC7C,MAAI,SAAS,CAAC,GACV,WAAW,CAAC,GACZ,WAAW,KAAK,YAAY,OAC5B,WAAW,KAAK,YAAY,KAC5B,gBAAgB,SAASyB,eAAc,MAAM,UAAU;AACzD,QAAI,WAAW,CAAC,GACZ,WAAW,CAAC,GACZ,QAAQjF,MAAK,YAAY,UAAU,WAAY;AACjD,eAAS,UAAU,QAAQ;AAC3B,iBAAW,CAAC;AACZ,iBAAW,CAAC;AAAA,IACd,CAAC,EAAE,MAAM;AACT,WAAO,SAAU,MAAM;AACrB,eAAS,UAAU,MAAM,QAAQ,IAAI;AACrC,eAAS,KAAK,KAAK,OAAO;AAC1B,eAAS,KAAK,IAAI;AAClB,kBAAY,SAAS,UAAU,MAAM,SAAS,CAAC;AAAA,IACjD;AAAA,EACF,GACI;AAEJ,OAAK,KAAK,MAAM;AACd,aAAS,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,MAAM,QAAQ,YAAY,KAAK,CAAC,CAAC,KAAK,MAAM,kBAAkB,cAAc,GAAG,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AAAA,EAC7H;AAEA,MAAI,YAAY,QAAQ,GAAG;AACzB,eAAW,SAAS;AAEpB,IAAA6C,cAAaW,gBAAe,WAAW,WAAY;AACjD,aAAO,WAAW,KAAK,SAAS;AAAA,IAClC,CAAC;AAAA,EACH;AAEA,WAAS,OAAO,EAAE,QAAQ,SAAU,QAAQ;AAC1C,QAAI,SAAS,CAAC;AAEd,SAAK,KAAK,UAAU;AAClB,aAAO,CAAC,IAAI,SAAS,CAAC;AAAA,IACxB;AAEA,WAAO,UAAU;AACjB,WAAO,KAAKA,eAAc,OAAO,MAAM,CAAC;AAAA,EAC1C,CAAC;AAED,SAAO;AACT;AAGA,IAAI,uCAAuC,SAAS0B,sCAAqC,YAAY,SAAS,KAAK,KAAK;AACtH,YAAU,MAAM,WAAW,GAAG,IAAI,UAAU,KAAK,WAAW,CAAC;AAC7D,SAAO,MAAM,OAAO,MAAM,YAAY,MAAM,WAAW,MAAM,IAAI,WAAW,UAAU,OAAO;AAC/F;AAHA,IAII,sBAAsB,SAASC,qBAAoB,QAAQ,WAAW;AACxE,MAAI,cAAc,MAAM;AACtB,WAAO,MAAM,eAAe,cAAc;AAAA,EAC5C,OAAO;AACL,WAAO,MAAM,cAAc,cAAc,OAAO,SAAS,YAAY,SAAS,aAAa,SAAS,UAAU,gBAAgB,MAAM;AAAA,EACtI;AAEA,aAAW/E,WAAU+E,qBAAoB9E,QAAO,SAAS;AAC3D;AAZA,IAaI,YAAY;AAAA,EACd,MAAM;AAAA,EACN,QAAQ;AACV;AAhBA,IAiBI,gBAAgB,SAAS+E,eAAc,OAAO;AAChD,MAAI,QAAQ,MAAM,OACd,SAAS,MAAM,QACf,OAAO,MAAM;AAEjB,MAAI,QAAQ,MAAM,iBAAiB,MAAM,eAAe,CAAC,IAAI,OAAO,QAChE,QAAQ,KAAK,SAASpF,MAAK,KAAK,SAAS,IAAI,GAC7C,OAAOW,UAAS,GAChB;AAEJ,MAAI,CAAC,MAAM,cAAc,OAAO,MAAM,aAAa,KAAM;AAEvD,WAAO,QAAQ,SAASN,WAAU,KAAK,gBAAgB,KAAK,gBAAgB,KAAK,eAAe,KAAK,eAAe,EAAE,WAAW,KAAK,kBAAkB,IAAI,GAAG,SAAS,KAAK,UAAU,GAAG,SAAS,KAAK;AACtM,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,YAAY,QAAQ,SAAS,UAAU,CAACgB,aAAY,IAAI,MAAM,WAAW,KAAK,kBAAkB,IAAI,GAAG,SAAS,KAAK,UAAU,GAAG,SAAS;AACjJ,UAAM,aAAa;AAAA,EACrB;AAEA,MAAI,MAAM,aAAa,SAAS,KAAK;AACnC,UAAM,gBAAgB;AACtB,UAAM,aAAa;AAAA,EACrB;AACF;AAzCA,IA2CA,iBAAiB,SAASgE,gBAAe,QAAQ,MAAM,QAAQ,QAAQ;AACrE,SAAO,SAAS,OAAO;AAAA,IACrB;AAAA,IACA,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV;AAAA,IACA,SAAS,SAAS,UAAU;AAAA,IAC5B,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU,SAAS,WAAW;AAC5B,aAAO,UAAUxC,cAAa1C,OAAM,SAAS,WAAW,CAAC,GAAG,gBAAgB,OAAO,IAAI;AAAA,IACzF;AAAA,IACA,WAAW,SAAS,YAAY;AAC9B,aAAO2C,iBAAgB3C,OAAM,SAAS,WAAW,CAAC,GAAG,gBAAgB,IAAI;AAAA,IAC3E;AAAA,EACF,CAAC;AACH;AA7DA,IA8DI,YAAY;AA9DhB,IA+DI;AA/DJ,IAgEI,iBAAiB,SAASmF,gBAAe,GAAG;AAC9C,MAAI,UAAU,UAAU,KAAK,EAAE,OAAO,OAAO;AAE7C,MAAI,WAAW,iBAAiB;AAC9B,MAAE,aAAa;AACf,sBAAkB;AAAA,EACpB;AACF;AAvEA,IAwEI,uBAAuB,SAASC,sBAAqB,MAAM;AAC7D,YAAU,IAAI,MAAM,OAAO,CAAC;AAC5B,OAAK,iBAAiB,KAAK,eAAe,KAAK,cAAc;AAC7D,OAAK,SAAS,KAAK,OAAO;AAC1B,OAAK,WAAW,CAAC,CAAC,KAAK;AACvB,OAAK,KAAK,KAAK,MAAM;AAErB,MAAI,SAAS,MACT,mBAAmB,OAAO,kBAC1B,WAAW,OAAO,UAClB,oBAAoB,OAAO,mBAC3B,YAAY,OAAO,WACnB,MACA,MACA,SAAS,WAAW,KAAK,MAAM,KAAKnF,SACpC,WAAWJ,MAAK,KAAK,QAAQ,EAAE,gBAC/B,mBAAmB,YAAY,SAAS,IAAI,GAC5C,UAAU,eAAe,KAAK,WAAW,WAAW,KAAK,OAAO,KAAK,oBAAoB,KAAK,YAAY,SAAS,CAAC,iBAAiB,OAAO,KAAK,iBAAiB,QAAQ,IAC1K,cAAc,eAAe,QAAQ,SAAS,GAC9C,cAAc,eAAe,QAAQ,WAAW,GAChD,QAAQ,GACR,gBAAgB,SAAS,WAAWE,MAAK,iBAAiBA,MAAK,eAAe,QAAQA,MAAK,eAAe,QAAQA,MAAK,cAAcA,MAAK,YAC1I,eAAe,GACf,0BAA0B,YAAY,QAAQ,IAAI,WAAY;AAChE,WAAO,SAAS,IAAI;AAAA,EACtB,IAAI,WAAY;AACd,WAAO,YAAY;AAAA,EACrB,GACI,eACA,eACA,gBAAgB,eAAe,QAAQ,KAAK,MAAM,MAAM,iBAAiB,GACzE,kBAAkB,SAASsF,mBAAkB;AAC/C,WAAO,gBAAgB;AAAA,EACzB,GACI,eAAe,cACf,eAAe,cACf,eAAe,SAASC,gBAAe;AACzC,WAAO,WAAW,QAAQ,SAAS;AACnC,mBAAelF,QAAO,aAAa,IAAI,GAAG,IAAI;AAC9C,yBAAqB,eAAeA,QAAO,GAAG,WAAW,QAAQ,WAAW,CAAC;AAC7E,oBAAgB;AAAA,EAClB,GACI,sBAAsB,SAASmF,uBAAsB;AACvD,YAAQ,MAAM,IAAI,OAAO,WAAW,QAAQ,MAAM,CAAC,IAAI,YAAY,MAAM,IAAI;AAC7E,YAAQ,MAAM,YAAY,qDAAqD,WAAW,QAAQ,MAAM,CAAC,IAAI;AAC7G,gBAAY,SAAS,YAAY,UAAU;AAAA,EAC7C,GACI,aAAa,SAASC,cAAa;AACrC,QAAI,eAAe;AACjB,4BAAsB,eAAe;AAErC,UAAI,SAAS,OAAO,KAAK,SAAS,CAAC,GAC/B,SAAS,aAAa,YAAY,IAAI,MAAM;AAEhD,UAAI,WAAW,WAAW,YAAY,IAAI,YAAY,QAAQ;AAC5D,oBAAY,SAAS,SAAS,YAAY;AAE1C,YAAI,IAAI,QAAQ,WAAW,WAAW,QAAQ,MAAM,CAAC,KAAK,KAAK,YAAY,MAAM;AAEjF,gBAAQ,MAAM,YAAY,qDAAqD,IAAI;AACnF,gBAAQ,MAAM,IAAI,IAAI;AACtB,oBAAY,UAAU,WAAW;AAEjC,mBAAW;AAAA,MACb;AAEA,aAAO;AAAA,IACT;AAEA,gBAAY,UAAU,oBAAoB;AAC1C,oBAAgB;AAAA,EAClB,GACI,OACA,cACA,cACA,mBACA,WAAW,SAASC,YAAW;AAEjC,iBAAa;AAEb,QAAI,MAAM,SAAS,KAAK,MAAM,KAAK,UAAU,MAAM;AACjD,kBAAY,IAAI,OAAO,MAAM,SAAS,CAAC,KAAK,YAAY,IAAI,IAAI,MAAM,QAAQ,WAAW,IAAI;AAAA,IAC/F;AAAA,EACF;AAEA,aAAW5F,MAAK,IAAI,SAAS;AAAA,IAC3B,GAAG;AAAA,EACL,CAAC;AAED,OAAK,cAAc,SAAU,GAAG;AAC9B,WAAO,cAAc,EAAE,SAAS,eAAe,WAAW,CAAC,KAAK,QAAQ,QAAQ,EAAE,SAAS,gBAAgB,KAAK,eAAe,EAAE,WAAW,EAAE,QAAQ,SAAS;AAAA,EACjK;AAEA,OAAK,UAAU,WAAY;AACzB,oBAAgB;AAChB,QAAI,YAAY;AAChB,YAAQ,QAAQE,MAAK,kBAAkBA,MAAK,eAAe,SAAS,KAAK,YAAY;AACrF,UAAM,MAAM;AACZ,kBAAc,SAAS,oBAAoB,QAAQ,QAAQ,OAAO,OAAO,mBAAmB,QAAQ,GAAG;AACvG,mBAAe,YAAY;AAC3B,mBAAe,YAAY;AAC3B,iBAAa;AACb,oBAAgB;AAAA,EAClB;AAEA,OAAK,YAAY,KAAK,iBAAiB,SAAU2F,OAAM,aAAa;AAClE,gBAAY,UAAU,oBAAoB;AAE1C,QAAI,CAAC,aAAa;AAChB,wBAAkB,QAAQ,IAAI;AAAA,IAChC,OAAO;AACL,iBAAW;AAGX,UAAI,MAAM,wBAAwB,GAC9B,eACA;AAEJ,UAAI,kBAAkB;AACpB,wBAAgB,YAAY;AAC5B,oBAAY,gBAAgB,MAAM,OAAO,CAACA,MAAK,YAAY;AAE3D,eAAO,qCAAqC,aAAa,eAAe,WAAW,WAAW,QAAQ,WAAW,CAAC;AAClH,cAAM,KAAK,UAAU,aAAa,SAAS;AAAA,MAC7C;AAEA,sBAAgB,YAAY;AAC5B,kBAAY,gBAAgB,MAAM,OAAO,CAACA,MAAK,YAAY;AAE3D,aAAO,qCAAqC,aAAa,eAAe,WAAW,WAAW,QAAQ,SAAS,CAAC;AAChH,YAAM,KAAK,UAAU,aAAa,SAAS;AAC3C,YAAM,WAAW,EAAE,SAAS,GAAG,EAAE,KAAK,IAAI;AAE1C,UAAI,cAAc,MAAM,KAAK,WAAW,QAAQ,iBAAiB,OAAO,GAAG;AAEzE,QAAA7F,MAAK,GAAG,CAAC,GAAG;AAAA,UACV,UAAU;AAAA,UACV,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA,IACF;AAEA,iBAAa,UAAU6F,KAAI;AAAA,EAC7B;AAEA,OAAK,UAAU,WAAY;AACzB,UAAM,OAAO,MAAM,MAAM;AAEzB,QAAIlF,UAAS,IAAI,eAAe,KAAM;AAEpC,sBAAgB;AAChB,qBAAeA,UAAS;AAAA,IAC1B;AAAA,EACF;AAEA,OAAK,WAAW,SAAUkF,OAAM,IAAI,IAAI,QAAQ,QAAQ;AACtD,mBAAe,iBAAiB,aAAa;AAC7C,UAAM,oBAAoB,YAAY,aAAa,OAAO,CAAC,MAAM,KAAK,gBAAgBA,MAAK,SAASA,MAAK,KAAK,YAAY,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC;AAE7I,QAAI,IAAI;AACN,kBAAY,UAAU,oBAAoB;AAC1C,UAAI,UAAU,OAAO,CAAC,MAAM,IACxB,IAAI,UAAU,eAAeA,MAAK,SAASA,MAAK,IAAI,YAAY,IAAI,KAAK,OAAO,CAAC,GACjF,WAAW,aAAa,CAAC;AAC7B,iBAAW,MAAM,aAAa,gBAAgB,WAAW;AACzD,kBAAY,QAAQ;AAAA,IACtB;AAEA,KAAC,MAAM,OAAO,WAAW;AAAA,EAC3B;AAEA,OAAK,WAAW,WAAY;AAC1B,wBAAoB,QAAQ,mBAAmB,QAAQ,GAAG;AAE1D,IAAArC,eAAc,iBAAiB,WAAW,QAAQ;AAElD,IAAAX,cAAa3C,OAAM,UAAU,QAAQ;AAErC,QAAI,YAAY,QAAQ;AACtB,kBAAY,OAAO,MAAM,iBAAiB;AAC1C,kBAAY,SAAS,YAAY,SAAS;AAAA,IAC5C;AAEA,kBAAc,OAAO;AAAA,EACvB;AAEA,OAAK,YAAY,WAAY;AAC3B,wBAAoB,QAAQ,IAAI;AAEhC,IAAA4C,iBAAgB5C,OAAM,UAAU,QAAQ;AAExC,IAAAsD,eAAc,oBAAoB,WAAW,QAAQ;AACrD,kBAAc,KAAK;AAAA,EACrB;AAEA,OAAK,WAAW,KAAK,aAAa;AAClC,SAAO,IAAI,SAAS,IAAI;AACxB,OAAK,MAAM;AAEX,gBAAc,CAAC,YAAY,KAAK,YAAY,CAAC;AAE7C,gBAAcxD,MAAK,OAAO,IAAI,YAAY;AAE1C,sBAAoB,KAAK;AACzB,UAAQA,MAAK,GAAG,MAAM;AAAA,IACpB,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS,mBAAmB,UAAU;AAAA,IACtC,SAAS;AAAA,IACT,WAAW;AAAA,MACT,SAAS,qBAAqB,aAAa,YAAY,GAAG,WAAY;AACpE,eAAO,MAAM,MAAM;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,IACA,UAAU;AAAA,IACV,YAAY,kBAAkB,KAAK;AAAA,EACrC,CAAC;AAED,SAAO;AACT;AAEAwD,eAAc,OAAO,SAAU,MAAM;AACnC,MAAI,YAAY,IAAI,GAAG;AACrB,WAAO,UAAU,KAAK,IAAI;AAAA,EAC5B;AAEA,MAAI,SAAStD,MAAK,eAAe;AACjC,EAAAsD,eAAc,OAAO,EAAE,QAAQ,SAAU,GAAG;AAC1C,WAAO,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,QAAQ,sBAAsB,EAAE,MAAM,EAAE,QAAQtD,MAAK;AAAA,EAChG,CAAC;AACD,SAAO,UAAU,KAAK,QAAQ,SAAU,GAAG,GAAG;AAC5C,YAAQ,EAAE,KAAK,mBAAmB,KAAK,QAAQ,EAAE,KAAK,qBAAqB,MAAM,EAAE,YAAY,EAAE,KAAK,qBAAqB,MAAM,EAAE,WAAW,EAAE,KAAK,mBAAmB,KAAK;AAAA,EAC/K,CAAC;AACH;AAEAsD,eAAc,UAAU,SAAU,MAAM;AACtC,SAAO,IAAI,SAAS,IAAI;AAC1B;AAEAA,eAAc,kBAAkB,SAAU,MAAM;AAC9C,MAAI,OAAO,SAAS,aAAa;AAC/B,WAAOhD;AAAA,EACT;AAEA,MAAI,SAAS,QAAQA,cAAa;AAChC,WAAOA,aAAY,OAAO;AAAA,EAC5B;AAEA,MAAI,SAAS,OAAO;AAClB,IAAAA,gBAAeA,aAAY,KAAK;AAChC,IAAAA,eAAc;AACd;AAAA,EACF;AAEA,MAAI,aAAa,gBAAgB,WAAW,OAAO,qBAAqB,IAAI;AAC5E,EAAAA,gBAAeA,aAAY,WAAW,WAAW,UAAUA,aAAY,KAAK;AAC5E,EAAAa,aAAY,WAAW,MAAM,MAAMb,eAAc;AACjD,SAAO;AACT;AAEAgD,eAAc,OAAO;AAAA;AAAA,EAEnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA;AAAA,IAEN,IAAI,SAAS,KAAK;AAChB,yBAAmB,UAAU,aAAa;AAC1C,wBAAkB7C,UAAS;AAAA,IAC7B;AAAA;AAAA,IAEA,KAAK,SAAS,MAAM;AAClB,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACAS,UAAS,KAAKpB,MAAK,eAAewD,cAAa;", "names": ["_getGSAP", "_bridge", "_integrate", "_getProxyProp", "_isViewport", "_addListener", "_removeListener", "_onScroll", "_scrollCacheFunc", "cachingFunc", "_getTarget", "_isWithin", "_getScrollFunc", "_getVelocityProp", "update", "value", "reset", "getVelocity", "_getEvent", "_getAbsoluteMax", "_setScrollTrigger", "_initCore", "Observer", "clickCapture", "_ignore<PERSON>heck", "onStopFunc", "onDelta", "onTouchOrPointerDelta", "_onDrag", "_onGestureStart", "_onGestureEnd", "onScroll", "_onWheel", "_onMove", "_onHover", "_onHoverEnd", "_onClick", "gsap", "_coreInitted", "_win", "_doc", "_docEl", "_body", "_root", "_clamp", "_normalizer", "_context", "_startup", "_getTime", "_parseClamp", "_keepClamp", "_rafBugFix", "_pointerDownHandler", "_pointerU<PERSON><PERSON><PERSON><PERSON>", "_passThrough", "_round", "_windowExists", "_getGSAP", "_isViewport", "_getViewportDimension", "_getBoundsFunc", "_getSizeFunc", "_getOffsetsFunc", "_maxScroll", "_iterateAutoRefresh", "_isString", "_isFunction", "_isNumber", "_isObject", "_endAnimation", "_callback", "_getComputedStyle", "_makePositionable", "_setDefaults", "_getBounds", "_getSize", "_getLabelRatioArray", "_getClosestLabel", "_snapDirectional", "a", "_getLabelAtDirection", "_multiListener", "_addListener", "_removeListener", "_wheelListener", "_offsetToPx", "_createMarker", "_position<PERSON><PERSON>er", "_sync", "_onScroll", "_setBaseDimensions", "_onResize", "_softRefresh", "ScrollTrigger", "_dispatch", "_revertRecorded", "_revertAll", "_clearScrollMemory", "_queueRefreshAll", "_refresh100vh", "_hideAllMarkers", "_refreshAll", "_updateAll", "_swapPinOut", "_swapPinIn", "_setState", "_getState", "_copyState", "_parsePosition", "_reparent", "_interruptionTracker", "_shiftMarker", "_getTweenCreator", "getTween", "offset", "cs", "prevProgress", "snap", "proxyCallback", "_clampScrollAndGetDurationMultiplier", "_allowNativePanning", "_nestedScroll", "_inputObserver", "_captureInputs", "_getScrollNormalizer", "resumeTouchMove", "updateClamps", "removeContentOffset", "ignoreDrag", "onResize", "self"]}