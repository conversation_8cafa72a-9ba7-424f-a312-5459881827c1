# OhMyKing's Home - 技术栈分析与开发指南

## 📋 项目概述

这是一个现代化的个人主页项目，融合了**国际主义风格**与**构成主义**设计理念，使用纯前端技术栈构建，具有丰富的3D视觉效果和流畅的动画交互。

## 🛠️ 核心技术栈

### 1. 前端基础技术
- **HTML5**: 语义化标记，响应式设计
- **CSS3**: 现代CSS特性，Flexbox/Grid布局，动画效果
- **JavaScript (ES6+)**: 模块化开发，异步编程

### 2. 3D图形与动画库
- **Three.js**: 核心3D渲染引擎
  - WebGL渲染器
  - 透视相机系统
  - 场景管理
  - 几何体和材质系统
  - 着色器编程 (GLSL)
- **GSAP (GreenSock)**: 高性能动画库
  - 时间轴动画
  - 缓动函数
  - DOM元素动画
- **Tween.js**: 补间动画库

### 3. 视觉效果库
- **Confetti.js**: 粒子效果系统
- **自定义着色器**: 
  - 噪声着色器 (Perlin Noise)
  - 模糊效果着色器
  - 晕影效果着色器
  - 后处理效果

### 4. 字体与UI
- **Google Fonts**: Cabin字体系列
- **SVG图形**: 矢量图标和装饰元素
- **响应式设计**: 移动端适配

## 🏗️ 项目架构

### 文件结构
```
project/
├── index.html              # 主页面
├── css/
│   ├── styles.css         # 主样式文件
│   └── css2.css          # Google Fonts样式
├── js/
│   ├── modules/           # 核心模块
│   │   ├── renderer.js    # Three.js渲染器配置
│   │   ├── post.js        # 后处理效果
│   │   ├── fbo.js         # 帧缓冲对象
│   │   ├── Maf.js         # 数学工具函数
│   │   ├── ShaderPass.js  # 着色器通道
│   │   └── bloomPass.js   # 辉光效果
│   ├── shaders/           # 着色器文件
│   │   ├── noise.js       # 噪声着色器
│   │   ├── blur.js        # 模糊着色器
│   │   ├── vignette.js    # 晕影着色器
│   │   └── screen.js      # 屏幕合成着色器
│   ├── third_party/       # 第三方库
│   │   ├── three.module.js
│   │   ├── OrbitControls.js
│   │   └── perlin.js
│   ├── page4.js           # 页面4逻辑
│   ├── page5.js           # 页面5逻辑
│   ├── page6.js           # 页面6逻辑
│   └── page7.js           # 页面7逻辑
├── assets/
│   ├── favicon.ico
│   └── *.woff2           # 字体文件
└── images/
    └── card1.png         # 项目展示图片
```

### 模块化设计
- **ES6模块系统**: 使用import/export进行模块管理
- **组件化开发**: 每个页面独立的JavaScript模块
- **着色器模块化**: 可复用的着色器组件

## 🎨 设计特色

### 视觉风格
- **极简主义**: 简洁的界面设计
- **几何美学**: 大量使用几何形状和线条
- **色彩搭配**: 红色主色调 (#f43a47) + 黑白对比
- **动态效果**: 流畅的过渡动画和3D变换

### 交互体验
- **无缝滚动**: 平滑的页面滚动体验
- **3D交互**: 鼠标控制的3D场景
- **响应式布局**: 适配不同屏幕尺寸
- **加载动画**: 优雅的页面加载效果

## 🚀 如何仿制类似项目

### 1. 环境准备
```bash
# 创建项目目录
mkdir my-portfolio
cd my-portfolio

# 初始化项目
npm init -y

# 安装依赖 (如果使用构建工具)
npm install three gsap
```

### 2. 基础结构搭建
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Portfolio</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- 页面内容 -->
    </div>
    
    <!-- 脚本引入 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script type="module" src="js/main.js"></script>
</body>
</html>
```

### 3. Three.js基础设置
```javascript
// js/renderer.js
import * as THREE from 'three';

// 创建渲染器
const renderer = new THREE.WebGLRenderer({
    alpha: true,
    antialias: true
});
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.setPixelRatio(window.devicePixelRatio);
document.body.appendChild(renderer.domElement);

// 创建场景和相机
const scene = new THREE.Scene();
const camera = new THREE.PerspectiveCamera(
    75, 
    window.innerWidth / window.innerHeight, 
    0.1, 
    1000
);

// 渲染循环
function animate() {
    requestAnimationFrame(animate);
    renderer.render(scene, camera);
}
animate();
```

### 4. GSAP动画集成
```javascript
// js/animations.js
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

// 页面加载动画
gsap.timeline()
    .from('.hero-title', { duration: 1, y: 100, opacity: 0 })
    .from('.hero-subtitle', { duration: 1, y: 50, opacity: 0 }, '-=0.5');

// 滚动触发动画
gsap.to('.section', {
    scrollTrigger: {
        trigger: '.section',
        start: 'top 80%',
        end: 'bottom 20%',
        scrub: true
    },
    y: -50,
    opacity: 1
});
```

### 5. 自定义着色器
```javascript
// js/shaders/vertex.js
export const vertexShader = `
    varying vec2 vUv;
    void main() {
        vUv = uv;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
    }
`;

// js/shaders/fragment.js
export const fragmentShader = `
    uniform float time;
    varying vec2 vUv;
    
    void main() {
        vec3 color = vec3(sin(vUv.x * 10.0 + time), cos(vUv.y * 10.0 + time), 0.5);
        gl_FragColor = vec4(color, 1.0);
    }
`;
```

## 📚 推荐学习资源

### Three.js学习
- [Three.js官方文档](https://threejs.org/docs/)
- [Three.js Journey课程](https://threejs-journey.com/)
- [Three.js Fundamentals](https://threejsfundamentals.org/)

### GSAP动画
- [GSAP官方文档](https://greensock.com/docs/)
- [GSAP学习指南](https://greensock.com/learning/)

### 着色器编程
- [Shadertoy](https://www.shadertoy.com/) - 在线着色器编辑器
- [The Book of Shaders](https://thebookofshaders.com/) - 着色器教程

### 设计灵感
- [Awwwards](https://www.awwwards.com/) - 优秀网页设计展示
- [Dribbble](https://dribbble.com/) - 设计作品分享平台

## 🔧 开发工具推荐

### 代码编辑器
- **Visual Studio Code**: 丰富的插件生态
- **WebStorm**: 强大的JavaScript IDE

### 调试工具
- **Chrome DevTools**: 浏览器开发者工具
- **Three.js Inspector**: Three.js场景调试
- **GSAP DevTools**: GSAP动画调试

### 构建工具
- **Vite**: 快速的前端构建工具
- **Webpack**: 模块打包器
- **Parcel**: 零配置构建工具

## 💡 开发建议

### 性能优化
1. **几何体复用**: 避免重复创建相同的几何体
2. **纹理优化**: 使用合适的纹理尺寸和格式
3. **着色器优化**: 避免复杂的片段着色器计算
4. **动画优化**: 使用requestAnimationFrame和适当的帧率控制

### 代码组织
1. **模块化开发**: 将功能拆分为独立模块
2. **配置分离**: 将配置项提取到单独文件
3. **错误处理**: 添加适当的错误处理机制
4. **代码注释**: 为复杂逻辑添加详细注释

### 用户体验
1. **加载优化**: 实现优雅的加载动画
2. **响应式设计**: 确保在不同设备上的良好体验
3. **无障碍访问**: 考虑键盘导航和屏幕阅读器支持
4. **性能监控**: 监控页面性能指标

## 🎯 具体实现示例

### 页面滚动控制
```javascript
// 平滑滚动实现
class SmoothScroll {
    constructor() {
        this.current = 0;
        this.target = 0;
        this.ease = 0.1;

        this.init();
    }

    init() {
        document.body.style.height = `${document.body.scrollHeight}px`;

        window.addEventListener('scroll', () => {
            this.target = window.scrollY;
        });

        this.animate();
    }

    animate() {
        this.current += (this.target - this.current) * this.ease;

        // 更新页面元素位置
        document.querySelector('#main-content').style.transform =
            `translateY(${-this.current}px)`;

        requestAnimationFrame(() => this.animate());
    }
}
```

### 3D场景管理
```javascript
// 场景管理器
class SceneManager {
    constructor() {
        this.scenes = new Map();
        this.currentScene = null;

        this.setupRenderer();
        this.setupCamera();
    }

    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({
            alpha: true,
            antialias: true,
            powerPreference: "high-performance"
        });

        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        document.body.appendChild(this.renderer.domElement);
    }

    addScene(name, sceneObject) {
        this.scenes.set(name, sceneObject);
    }

    switchScene(name) {
        if (this.scenes.has(name)) {
            this.currentScene = this.scenes.get(name);
        }
    }

    render() {
        if (this.currentScene) {
            this.renderer.render(this.currentScene.scene, this.currentScene.camera);
        }
        requestAnimationFrame(() => this.render());
    }
}
```

### 粒子系统
```javascript
// 粒子效果实现
class ParticleSystem {
    constructor(count = 1000) {
        this.count = count;
        this.particles = new THREE.BufferGeometry();

        this.createParticles();
        this.createMaterial();
        this.createMesh();
    }

    createParticles() {
        const positions = new Float32Array(this.count * 3);
        const colors = new Float32Array(this.count * 3);

        for (let i = 0; i < this.count; i++) {
            const i3 = i * 3;

            // 位置
            positions[i3] = (Math.random() - 0.5) * 10;
            positions[i3 + 1] = (Math.random() - 0.5) * 10;
            positions[i3 + 2] = (Math.random() - 0.5) * 10;

            // 颜色
            colors[i3] = Math.random();
            colors[i3 + 1] = Math.random();
            colors[i3 + 2] = Math.random();
        }

        this.particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        this.particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    }

    createMaterial() {
        this.material = new THREE.PointsMaterial({
            size: 0.1,
            vertexColors: true,
            transparent: true,
            opacity: 0.8
        });
    }

    createMesh() {
        this.mesh = new THREE.Points(this.particles, this.material);
    }

    update(time) {
        const positions = this.particles.attributes.position.array;

        for (let i = 0; i < this.count; i++) {
            const i3 = i * 3;
            positions[i3 + 1] += Math.sin(time + i) * 0.01;
        }

        this.particles.attributes.position.needsUpdate = true;
    }
}
```

## 🔍 关键技术细节

### 着色器后处理管道
```javascript
// 后处理效果链
class PostProcessing {
    constructor(renderer, scene, camera) {
        this.renderer = renderer;
        this.scene = scene;
        this.camera = camera;

        this.setupRenderTargets();
        this.setupPasses();
    }

    setupRenderTargets() {
        const size = this.renderer.getSize(new THREE.Vector2());

        this.renderTarget1 = new THREE.WebGLRenderTarget(size.x, size.y);
        this.renderTarget2 = new THREE.WebGLRenderTarget(size.x, size.y);
    }

    setupPasses() {
        // 模糊通道
        this.blurPass = new BlurPass();

        // 辉光通道
        this.bloomPass = new BloomPass();

        // 最终合成通道
        this.finalPass = new FinalPass();
    }

    render() {
        // 渲染到纹理
        this.renderer.setRenderTarget(this.renderTarget1);
        this.renderer.render(this.scene, this.camera);

        // 应用后处理效果
        this.blurPass.render(this.renderTarget1, this.renderTarget2);
        this.bloomPass.render(this.renderTarget2, this.renderTarget1);

        // 渲染到屏幕
        this.renderer.setRenderTarget(null);
        this.finalPass.render(this.renderTarget1);
    }
}
```

### 响应式设计实现
```css
/* 响应式断点 */
@media (max-width: 768px) {
    .mobile-warning {
        display: flex;
    }

    #main-content {
        display: none;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .hero-title {
        font-size: 4rem;
    }

    .section {
        padding: 2rem;
    }
}

@media (min-width: 1025px) {
    .hero-title {
        font-size: 6rem;
    }

    .section {
        padding: 4rem;
    }
}
```

## 📦 项目部署

### 静态部署
```bash
# 使用 GitHub Pages
git add .
git commit -m "Deploy website"
git push origin main

# 使用 Netlify
npm run build
netlify deploy --prod --dir=dist

# 使用 Vercel
vercel --prod
```

### 性能优化清单
- [ ] 压缩JavaScript和CSS文件
- [ ] 优化图片格式和尺寸
- [ ] 启用Gzip压缩
- [ ] 使用CDN加速资源加载
- [ ] 实现懒加载
- [ ] 添加Service Worker缓存

---

*本文档基于对OhMyKing主页项目的深入分析，为开发类似项目提供技术指导和最佳实践建议。*

## 📞 技术支持

如果在开发过程中遇到问题，可以参考以下资源：
- [项目GitHub仓库](https://github.com/OhMyKing/home)
- [Three.js社区论坛](https://discourse.threejs.org/)
- [GSAP社区支持](https://greensock.com/forums/)

**最后更新**: 2025年8月26日
